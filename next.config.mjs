/** @type {import('next').NextConfig} */
const nextConfig = {
  // 生产环境优化
  compress: true,
  poweredByHeader: false,
  reactStrictMode: true,
  
  // 图片优化配置
  images: {
    domains: ['placehold.co'], // 添加允许的图片域名
    formats: ['image/avif', 'image/webp'],
    minimumCacheTTL: 60,
  },
  
  // 注意：i18n 配置与 App Router 不兼容，已移除
  // 如需国际化，请使用 next-intl 等第三方库实现
  
  // 环境变量
  env: {
    CUSTOM_BUILD_TIME: new Date().toISOString(),
  },
  
  // 头部安全配置
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'SAMEORIGIN'
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff'
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin'
          },
          {
            key: 'X-DNS-Prefetch-Control',
            value: 'on'
          }
        ]
      }
    ]
  },
  
  // 重写规则 (如需要)
  async rewrites() {
    return [
      // 示例：API 代理
      // {
      //   source: '/api/proxy/:path*',
      //   destination: 'https://api.example.com/:path*'
      // }
    ]
  },
  
  // 输出配置 (根据部署平台调整)
  output: process.env.BUILD_STANDALONE === 'true' ? 'standalone' : undefined,
  
  // 服务器组件外部包配置（已移出 experimental）
  serverExternalPackages: [],
  
  // 开发环境跨域请求配置
  ...(process.env.NODE_ENV === 'development' && {
    experimental: {
      // 允许的开发环境跨域来源
      allowedDevOrigins: [
        'localhost:3000',
        '127.0.0.1:3000',
        '0.0.0.0:3000'
      ]
    }
  }),
}

export default nextConfig