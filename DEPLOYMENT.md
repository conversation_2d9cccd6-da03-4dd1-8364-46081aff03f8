# Quintar - 麒麟OS虚拟化产品网站 - 部署指南

## 🚀 部署概述

本项目支持多种部署方式，包括本地开发、Docker容器化部署、以及云平台部署。

## 📋 环境要求

- Node.js 18+ 
- npm 或 yarn 包管理器
- Docker (可选，用于容器化部署)
- Git

## 🏗️ 构建和测试

### 本地开发

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 访问 http://localhost:3000
```

### 生产构建

```bash
# 安装依赖
npm ci

# 运行测试
npm test

# 代码检查
npm run lint

# 构建项目
npm run build

# 启动生产服务器
npm start
```

## 🐳 Docker 部署

### 使用 Docker Compose (推荐)

```bash
# 构建并启动服务
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

### 使用 Dockerfile

```bash
# 构建镜像
docker build -t quintar .

# 运行容器
docker run -p 3000:3000 quintar
```

## 🚀 自动化部署

### 使用部署脚本

项目提供了自动化部署脚本：

```bash
# 部署到生产环境
./scripts/deploy.sh production

# 部署到开发环境
./scripts/deploy.sh development
```

### CI/CD 流程

项目配置了 GitHub Actions 工作流：

1. **测试阶段**: 运行 lint、类型检查、单元测试
2. **构建阶段**: 构建生产版本
3. **性能测试**: 使用 Lighthouse 进行性能测试
4. **部署阶段**: 自动部署到生产环境

## ☁️ 云平台部署

### Vercel 部署

1. 连接 GitHub 仓库到 Vercel
2. 设置环境变量（如需要）
3. 自动部署

```bash
# 或使用 Vercel CLI
npx vercel --prod
```

### Netlify 部署

1. 连接 GitHub 仓库到 Netlify
2. 构建设置：
   - 构建命令: `npm run build`
   - 发布目录: `.next`
3. 配置环境变量

### 阿里云/腾讯云部署

1. 购买 ECS 服务器
2. 安装 Node.js 和 Docker
3. 克隆项目并运行部署脚本
4. 配置 Nginx 反向代理（可选）

## 🔧 环境配置

### 环境变量

创建 `.env.local` 文件配置环境变量：

```env
# 应用配置
NODE_ENV=production
PORT=3000

# SEO 配置
NEXT_PUBLIC_SITE_URL=https://your-domain.com

# 分析工具 (可选)
NEXT_PUBLIC_GA_ID=GA_MEASUREMENT_ID
```

### 数据库连接 (如需要)

```env
# 数据库配置
DATABASE_URL=your_database_url
REDIS_URL=your_redis_url
```

## 📊 性能监控

### 健康检查

应用提供健康检查端点：

```bash
curl http://localhost:3000/api/health
```

### 日志监控

```bash
# 查看 Docker 日志
docker-compose logs -f

# 查看应用性能指标
curl http://localhost:3000/api/health
```

## 🔍 故障排除

### 常见问题

1. **构建失败**
   ```bash
   # 清除缓存重新安装
   rm -rf node_modules .next
   npm ci
   npm run build
   ```

2. **端口被占用**
   ```bash
   # 查找占用端口的进程
   lsof -i :3000
   
   # 杀死进程
   kill -9 <PID>
   ```

3. **Docker 容器启动失败**
   ```bash
   # 查看详细日志
   docker-compose logs
   
   # 重新构建镜像
   docker-compose build --no-cache
   ```

### 性能优化

1. **启用 CDN**: 将静态资源部署到 CDN
2. **数据库优化**: 启用查询缓存和索引
3. **图片优化**: 使用 WebP 格式和适当的压缩
4. **缓存策略**: 配置适当的缓存头

## 📝 维护建议

1. **定期更新依赖**: 使用 `npm audit` 检查安全漏洞
2. **备份数据**: 定期备份数据库和配置文件
3. **监控性能**: 使用 Lighthouse 等工具定期检查性能
4. **安全检查**: 定期运行安全扫描

## 🆘 支持和反馈

如遇到部署问题，请：

1. 查看项目日志和错误信息
2. 检查环境配置是否正确
3. 参考本文档的故障排除部分
4. 提交 Issue 或联系开发团队