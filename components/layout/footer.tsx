import Link from 'next/link';

export default function Footer() {
  return (
    <footer className="bg-gray-900 text-white">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold">Q</span>
              </div>
              <span className="text-xl font-bold">Quintar</span>
            </div>
            <p className="text-gray-400 text-sm">
              Quintar - 专为麒麟操作系统打造的专业虚拟化解决方案，让您在国产操作系统上无缝运行Windows应用程序。
            </p>
            <div className="flex space-x-4">
              <Link href="#" className="text-gray-400 hover:text-white">
                微信
              </Link>
              <Link href="#" className="text-gray-400 hover:text-white">
                QQ
              </Link>
              <Link href="#" className="text-gray-400 hover:text-white">
                微博
              </Link>
            </div>
          </div>

          {/* Products */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">产品</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/products/personal" className="text-gray-400 hover:text-white text-sm">
                  个人版
                </Link>
              </li>
              <li>
                <Link href="/products/professional" className="text-gray-400 hover:text-white text-sm">
                  专业版
                </Link>
              </li>
              <li>
                <Link href="/products/enterprise" className="text-gray-400 hover:text-white text-sm">
                  企业版
                </Link>
              </li>
              <li>
                <Link href="/download" className="text-gray-400 hover:text-white text-sm">
                  免费试用
                </Link>
              </li>
            </ul>
          </div>

          {/* Support */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">支持</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/support/docs" className="text-gray-400 hover:text-white text-sm">
                  技术文档
                </Link>
              </li>
              <li>
                <Link href="/support/faq" className="text-gray-400 hover:text-white text-sm">
                  常见问题
                </Link>
              </li>
              <li>
                <Link href="/support/community" className="text-gray-400 hover:text-white text-sm">
                  社区论坛
                </Link>
              </li>
              <li>
                <Link href="/support/contact" className="text-gray-400 hover:text-white text-sm">
                  联系我们
                </Link>
              </li>
            </ul>
          </div>

          {/* Company */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">公司</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/about" className="text-gray-400 hover:text-white text-sm">
                  关于我们
                </Link>
              </li>
              <li>
                <Link href="/customers" className="text-gray-400 hover:text-white text-sm">
                  客户案例
                </Link>
              </li>
              <li>
                <Link href="/partners" className="text-gray-400 hover:text-white text-sm">
                  合作伙伴
                </Link>
              </li>
              <li>
                <Link href="/careers" className="text-gray-400 hover:text-white text-sm">
                  加入我们
                </Link>
              </li>
            </ul>
          </div>
        </div>

        <div className="border-t border-gray-800 mt-12 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400 text-sm">
              © 2024 Quintar科技有限公司. 保留所有权利。
            </p>
            <div className="flex space-x-6 mt-4 md:mt-0">
              <Link href="/privacy" className="text-gray-400 hover:text-white text-sm">
                隐私政策
              </Link>
              <Link href="/terms" className="text-gray-400 hover:text-white text-sm">
                服务条款
              </Link>
              <Link href="/security" className="text-gray-400 hover:text-white text-sm">
                安全声明
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}