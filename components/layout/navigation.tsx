'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { 
  NavigationMenu, 
  NavigationMenuContent, 
  NavigationMenuItem, 
  NavigationMenuLink, 
  NavigationMenuList, 
  NavigationMenuTrigger 
} from '@/components/ui/navigation-menu';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import { Menu, X } from 'lucide-react';

const productLinks = [
  { title: '个人版', href: '/products/personal', description: '适合个人用户和小型团队' },
  { title: '专业版', href: '/products/professional', description: '适合专业开发者和中小企业' },
  { title: '企业版', href: '/products/enterprise', description: '适合大型企业和政府机构' }
];

const solutionLinks = [
  { title: '麒麟OS特色', href: '/kylin-os', description: '专为麒麟操作系统优化' },
  { title: '企业解决方案', href: '/solutions/enterprise', description: '企业级虚拟化解决方案' },
  { title: '开发者工具', href: '/solutions/developers', description: '开发者专用功能' }
];

const supportLinks = [
  { title: '文档中心', href: '/support/docs', description: '技术文档和使用指南' },
  { title: '常见问题', href: '/support/faq', description: '常见问题解答' },
  { title: '社区论坛', href: '/support/community', description: '用户社区讨论' },
  { title: '联系支持', href: '/support/contact', description: '技术支持服务' }
];

export default function Navigation() {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <nav className="bg-white border-b shadow-sm">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold">Q</span>
            </div>
            <span className="text-xl font-bold text-gray-900">Quintar</span>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            <NavigationMenu>
              <NavigationMenuList>
                {/* 产品 */}
                <NavigationMenuItem>
                  <NavigationMenuTrigger>产品</NavigationMenuTrigger>
                  <NavigationMenuContent>
                    <ul className="grid w-[400px] gap-3 p-4 md:w-[500px] md:grid-cols-2 lg:w-[600px]">
                      {productLinks.map((link) => (
                        <li key={link.href}>
                          <NavigationMenuLink asChild>
                            <Link
                              href={link.href}
                              className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground"
                            >
                              <div className="text-sm font-medium leading-none">{link.title}</div>
                              <p className="line-clamp-2 text-sm leading-snug text-muted-foreground">
                                {link.description}
                              </p>
                            </Link>
                          </NavigationMenuLink>
                        </li>
                      ))}
                    </ul>
                  </NavigationMenuContent>
                </NavigationMenuItem>

                {/* 解决方案 */}
                <NavigationMenuItem>
                  <NavigationMenuTrigger>解决方案</NavigationMenuTrigger>
                  <NavigationMenuContent>
                    <ul className="grid w-[400px] gap-3 p-4 md:w-[500px] md:grid-cols-2 lg:w-[600px]">
                      {solutionLinks.map((link) => (
                        <li key={link.href}>
                          <NavigationMenuLink asChild>
                            <Link
                              href={link.href}
                              className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground"
                            >
                              <div className="text-sm font-medium leading-none">{link.title}</div>
                              <p className="line-clamp-2 text-sm leading-snug text-muted-foreground">
                                {link.description}
                              </p>
                            </Link>
                          </NavigationMenuLink>
                        </li>
                      ))}
                    </ul>
                  </NavigationMenuContent>
                </NavigationMenuItem>

                {/* 技术支持 */}
                <NavigationMenuItem>
                  <NavigationMenuTrigger>技术支持</NavigationMenuTrigger>
                  <NavigationMenuContent>
                    <ul className="grid w-[400px] gap-3 p-4 md:w-[500px] md:grid-cols-2 lg:w-[600px]">
                      {supportLinks.map((link) => (
                        <li key={link.href}>
                          <NavigationMenuLink asChild>
                            <Link
                              href={link.href}
                              className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground"
                            >
                              <div className="text-sm font-medium leading-none">{link.title}</div>
                              <p className="line-clamp-2 text-sm leading-snug text-muted-foreground">
                                {link.description}
                              </p>
                            </Link>
                          </NavigationMenuLink>
                        </li>
                      ))}
                    </ul>
                  </NavigationMenuContent>
                </NavigationMenuItem>
              </NavigationMenuList>
            </NavigationMenu>

            <Link href="/customers" className="text-sm font-medium hover:text-blue-600">
              客户案例
            </Link>
            <Link href="/partners" className="text-sm font-medium hover:text-blue-600">
              合作伙伴
            </Link>
          </div>

          {/* CTA Buttons */}
          <div className="hidden md:flex items-center space-x-4">
            <Button variant="outline" asChild>
              <Link href="/download">免费试用</Link>
            </Button>
            <Button asChild>
              <Link href="/products">立即购买</Link>
            </Button>
          </div>

          {/* Mobile Menu Button */}
          <Sheet open={isOpen} onOpenChange={setIsOpen}>
            <SheetTrigger asChild>
              <Button variant="ghost" size="sm" className="md:hidden p-2">
                {isOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
              </Button>
            </SheetTrigger>
            <SheetContent side="right" className="w-[300px] sm:w-[400px]">
              <div className="flex flex-col space-y-4 mt-4">
                <Link href="/products" className="text-lg font-medium" onClick={() => setIsOpen(false)}>
                  产品
                </Link>
                <Link href="/solutions" className="text-lg font-medium" onClick={() => setIsOpen(false)}>
                  解决方案
                </Link>
                <Link href="/support" className="text-lg font-medium" onClick={() => setIsOpen(false)}>
                  技术支持
                </Link>
                <Link href="/customers" className="text-lg font-medium" onClick={() => setIsOpen(false)}>
                  客户案例
                </Link>
                <Link href="/partners" className="text-lg font-medium" onClick={() => setIsOpen(false)}>
                  合作伙伴
                </Link>
                <div className="pt-4 border-t">
                  <Button variant="outline" className="w-full mb-2" asChild>
                    <Link href="/download" onClick={() => setIsOpen(false)}>免费试用</Link>
                  </Button>
                  <Button className="w-full" asChild>
                    <Link href="/products" onClick={() => setIsOpen(false)}>立即购买</Link>
                  </Button>
                </div>
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </nav>
  );
}