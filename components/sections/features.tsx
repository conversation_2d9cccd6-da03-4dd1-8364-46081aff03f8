import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { coreFeatures } from '@/lib/data/products';
import { CheckCircle } from 'lucide-react';

export default function Features() {
  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="text-center mb-16">
            <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-200 mb-4">
              核心特性
            </Badge>
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              为什么选择麒麟虚拟化？
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              专为麒麟操作系统设计的虚拟化解决方案，结合了先进的虚拟化技术和深度的系统优化，
              为您提供无与伦比的性能和稳定性。
            </p>
          </div>

          {/* Features Grid */}
          <div className="grid lg:grid-cols-2 gap-8 mb-16">
            {coreFeatures.map((feature, index) => (
              <Card key={index} className="border-2 hover:border-blue-200 transition-colors">
                <CardHeader>
                  <div className="flex items-center space-x-3">
                    <div className="text-3xl">{feature.icon}</div>
                    <div>
                      <CardTitle className="text-xl">{feature.title}</CardTitle>
                      <CardDescription className="text-gray-600 mt-1">
                        {feature.description}
                      </CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {feature.benefits.map((benefit, benefitIndex) => (
                      <li key={benefitIndex} className="flex items-center space-x-2">
                        <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                        <span className="text-sm text-gray-700">{benefit}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Detailed Features */}
          <div className="bg-gray-50 rounded-2xl p-8 lg:p-12">
            <h3 className="text-2xl font-bold text-gray-900 mb-8 text-center">
              完整的虚拟化功能
            </h3>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[
                {
                  title: '一键部署',
                  description: '简化的安装流程，5分钟内完成虚拟机部署',
                  icon: '⚡'
                },
                {
                  title: '文件共享',
                  description: '主机与虚拟机之间拖拽文件，无缝数据交换',
                  icon: '📁'
                },
                {
                  title: '快照备份',
                  description: '系统快照和恢复，保护您的工作成果',
                  icon: '💾'
                },
                {
                  title: '网络配置',
                  description: '灵活的网络配置，支持桥接、NAT等多种模式',
                  icon: '🌐'
                },
                {
                  title: '硬件支持',
                  description: '完整的硬件虚拟化支持，包括GPU加速',
                  icon: '🔧'
                },
                {
                  title: '安全隔离',
                  description: '完全隔离的虚拟环境，保护主机系统安全',
                  icon: '🔒'
                }
              ].map((item, index) => (
                <div key={index} className="text-center">
                  <div className="text-3xl mb-3">{item.icon}</div>
                  <h4 className="font-semibold text-gray-900 mb-2">{item.title}</h4>
                  <p className="text-sm text-gray-600">{item.description}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}