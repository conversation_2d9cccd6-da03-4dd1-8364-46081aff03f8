import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import Link from 'next/link';
import { ArrowRight, Play, Shield, Zap, Users } from 'lucide-react';

export default function Hero() {
  return (
    <section className="bg-gradient-to-br from-blue-50 via-white to-indigo-50 pt-20 pb-16">
      <div className="container mx-auto px-4">
        <div className="max-w-7xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Left Content */}
            <div className="space-y-8">
              <div className="space-y-4">
                <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-200">
                  🚀 Quintar - 专为麒麟OS优化的虚拟化解决方案
                </Badge>
                <h1 className="text-4xl md:text-6xl font-bold text-gray-900 leading-tight">
                  在<span className="text-blue-600">麒麟OS</span>上
                  <br />
                  畅享<span className="text-blue-600">Windows</span>应用
                </h1>
                <p className="text-xl text-gray-600 max-w-2xl">
                  Quintar是专业的国产化虚拟化软件，让您在麒麟操作系统上无缝运行Windows应用程序。
                  安全、稳定、高性能，为企业数字化转型提供强有力支撑。
                </p>
              </div>

              {/* Stats */}
              <div className="grid grid-cols-3 gap-3 sm:gap-6 py-6">
                <div className="text-center">
                  <div className="flex items-center justify-center w-12 h-12 bg-blue-100 rounded-lg mb-2 mx-auto">
                    <Shield className="w-6 h-6 text-blue-600" />
                  </div>
                  <div className="text-2xl font-bold text-gray-900">100%</div>
                  <div className="text-sm text-gray-600">安全合规</div>
                </div>
                <div className="text-center">
                  <div className="flex items-center justify-center w-12 h-12 bg-green-100 rounded-lg mb-2 mx-auto">
                    <Zap className="w-6 h-6 text-green-600" />
                  </div>
                  <div className="text-2xl font-bold text-gray-900">95%</div>
                  <div className="text-sm text-gray-600">性能保持</div>
                </div>
                <div className="text-center">
                  <div className="flex items-center justify-center w-12 h-12 bg-purple-100 rounded-lg mb-2 mx-auto">
                    <Users className="w-6 h-6 text-purple-600" />
                  </div>
                  <div className="text-2xl font-bold text-gray-900">10000+</div>
                  <div className="text-sm text-gray-600">企业信赖</div>
                </div>
              </div>

              {/* CTA Buttons */}
              <div className="flex flex-col sm:flex-row gap-4 w-full">
                <Button size="lg" className="bg-blue-600 hover:bg-blue-700 w-full sm:w-auto min-h-[48px]" asChild>
                  <Link href="/download">
                    免费试用 30 天
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
                <Button size="lg" variant="outline" className="border-gray-300 w-full sm:w-auto min-h-[48px]" asChild>
                  <Link href="/demo">
                    <Play className="mr-2 h-4 w-4" />
                    观看演示
                  </Link>
                </Button>
              </div>

              {/* Trust Indicators */}
              <div className="flex items-center space-x-4 text-sm text-gray-600">
                <span>✓ 30天免费试用</span>
                <span>✓ 无需信用卡</span>
                <span>✓ 专业技术支持</span>
              </div>
            </div>

            {/* Right Content - Product Preview */}
            <div className="relative mt-8 lg:mt-0">
              <div className="relative bg-white rounded-2xl shadow-2xl overflow-hidden mx-auto max-w-md lg:max-w-none">
                <div className="bg-gray-100 h-8 flex items-center justify-between px-4">
                  <div className="flex space-x-2">
                    <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                    <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  </div>
                  <div className="text-xs text-gray-600">Quintar</div>
                </div>
                <div className="p-6 space-y-4">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                      <span className="text-white font-bold text-sm">Q</span>
                    </div>
                    <div>
                      <div className="font-semibold text-gray-900">麒麟OS 桌面版</div>
                      <div className="text-sm text-gray-600">版本 4.0.2</div>
                    </div>
                  </div>
                  <div className="bg-gray-50 rounded-lg p-4">
                    <div className="flex items-center space-x-3 mb-3">
                      <div className="w-6 h-6 bg-blue-500 rounded flex items-center justify-center">
                        <span className="text-white text-xs">Win</span>
                      </div>
                      <div>
                        <div className="font-medium text-gray-900">Windows 11 专业版</div>
                        <div className="text-sm text-gray-600">运行中 • 4GB RAM • 2 CPU</div>
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-3">
                      <div className="bg-white p-3 rounded border">
                        <div className="text-xs text-gray-600">Word</div>
                        <div className="text-sm font-medium">正在运行</div>
                      </div>
                      <div className="bg-white p-3 rounded border">
                        <div className="text-xs text-gray-600">Excel</div>
                        <div className="text-sm font-medium">正在运行</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Floating Elements */}
              <div className="absolute -top-4 -right-4 bg-green-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                运行中
              </div>
              <div className="absolute -bottom-4 -left-4 bg-blue-600 text-white px-3 py-1 rounded-full text-sm font-medium">
                高性能
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}