import { Badge } from '@/components/ui/badge';

export default function TrustedBy() {
  // 模拟的客户logo数据
  const customers = [
    { name: '中国银行', logo: '/api/placeholder/logo/bank-of-china' },
    { name: '华为技术', logo: '/api/placeholder/logo/huawei' },
    { name: '中国石油', logo: '/api/placeholder/logo/cnpc' },
    { name: '国家电网', logo: '/api/placeholder/logo/state-grid' },
    { name: '中国移动', logo: '/api/placeholder/logo/china-mobile' },
    { name: '阿里巴巴', logo: '/api/placeholder/logo/alibaba' },
    { name: '腾讯科技', logo: '/api/placeholder/logo/tencent' },
    { name: '百度公司', logo: '/api/placeholder/logo/baidu' },
    { name: '京东集团', logo: '/api/placeholder/logo/jd' },
    { name: '字节跳动', logo: '/api/placeholder/logo/bytedance' },
    { name: '小米科技', logo: '/api/placeholder/logo/xiaomi' },
    { name: '联想集团', logo: '/api/placeholder/logo/lenovo' }
  ];

  return (
    <section className="py-16 bg-white border-t border-gray-100">
      <div className="container mx-auto px-4">
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="text-center mb-12">
            <Badge className="bg-green-100 text-green-800 hover:bg-green-200 mb-4">
              客户信赖
            </Badge>
            <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4">
              超过10,000家企业的信赖之选
            </h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              从政府机构到科技企业，从金融机构到制造业，各行各业都在使用我们的虚拟化解决方案
            </p>
          </div>

          {/* Customer Logos */}
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-8 items-center justify-items-center">
            {customers.map((customer, index) => (
              <div
                key={index}
                className="flex items-center justify-center p-4 grayscale hover:grayscale-0 transition-all duration-300"
              >
                <div className="w-24 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                  <span className="text-sm font-medium text-gray-600">
                    {customer.name}
                  </span>
                </div>
              </div>
            ))}
          </div>

          {/* Stats */}
          <div className="mt-16 grid grid-cols-2 md:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-600 mb-2">10,000+</div>
              <div className="text-sm text-gray-600">企业客户</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-green-600 mb-2">500,000+</div>
              <div className="text-sm text-gray-600">活跃用户</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-purple-600 mb-2">99.9%</div>
              <div className="text-sm text-gray-600">系统稳定性</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-orange-600 mb-2">24/7</div>
              <div className="text-sm text-gray-600">技术支持</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}