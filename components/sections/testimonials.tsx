import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Star, Quote } from 'lucide-react';

export default function Testimonials() {
  const testimonials = [
    {
      name: '张伟',
      role: '系统架构师',
      company: '某大型银行',
      content: '作为银行的系统架构师，我们对安全性要求极高。麒麟虚拟化不仅满足了我们的安全需求，还大大提高了开发效率。在麒麟OS上运行Windows开发工具变得如此简单。',
      rating: 5,
      avatar: '/api/placeholder/avatar/zhang-wei'
    },
    {
      name: '李明',
      role: '技术总监',
      company: '某科技企业',
      content: '我们公司全面使用麒麟OS，但还需要一些Windows专用软件。麒麟虚拟化完美解决了这个问题，性能损耗很小，用户体验excellent。强烈推荐给同行。',
      rating: 5,
      avatar: '/api/placeholder/avatar/li-ming'
    },
    {
      name: '王芳',
      role: '运维工程师',
      company: '某政府机构',
      content: '政府部门对国产化软件要求很高，麒麟虚拟化让我们在保证安全的前提下，还能使用必要的Windows应用。技术支持也很及时，解决问题很快。',
      rating: 5,
      avatar: '/api/placeholder/avatar/wang-fang'
    },
    {
      name: '赵强',
      role: '软件开发工程师',
      company: '某制造企业',
      content: '制造业有很多专业软件只能在Windows上运行，但公司要求使用麒麟OS。麒麟虚拟化让我们能够在不改变工作流程的情况下，完成系统迁移。',
      rating: 5,
      avatar: '/api/placeholder/avatar/zhao-qiang'
    },
    {
      name: '陈敏',
      role: 'IT经理',
      company: '某教育机构',
      content: '教育行业需要兼容各种教学软件，麒麟虚拟化让我们的老师可以在麒麟OS上使用熟悉的Windows教学工具，大大减少了培训成本。',
      rating: 5,
      avatar: '/api/placeholder/avatar/chen-min'
    },
    {
      name: '孙磊',
      role: '系统管理员',
      company: '某医疗机构',
      content: '医疗行业对系统稳定性要求很高，麒麟虚拟化在我们医院运行两年多，非常稳定。而且符合医疗数据安全的各种规范要求。',
      rating: 5,
      avatar: '/api/placeholder/avatar/sun-lei'
    }
  ];

  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="text-center mb-16">
            <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-200 mb-4">
              用户评价
            </Badge>
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              用户怎么说
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              听听真实用户的声音，了解麒麟虚拟化如何帮助他们解决实际问题
            </p>
          </div>

          {/* Testimonials Grid */}
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <Card key={index} className="border-0 shadow-lg hover:shadow-xl transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-center mb-4">
                    <Quote className="w-8 h-8 text-blue-600 mr-3" />
                    <div className="flex">
                      {[...Array(testimonial.rating)].map((_, i) => (
                        <Star key={i} className="w-4 h-4 text-yellow-400 fill-current" />
                      ))}
                    </div>
                  </div>
                  
                  <p className="text-gray-700 mb-6 leading-relaxed">
                    &ldquo;{testimonial.content}&rdquo;
                  </p>
                  
                  <div className="flex items-center">
                    <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mr-4">
                      <span className="text-white font-semibold">
                        {testimonial.name.charAt(0)}
                      </span>
                    </div>
                    <div>
                      <div className="font-semibold text-gray-900">{testimonial.name}</div>
                      <div className="text-sm text-gray-600">{testimonial.role}</div>
                      <div className="text-sm text-blue-600">{testimonial.company}</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* CTA */}
          <div className="text-center mt-16">
            <div className="bg-white rounded-2xl p-8 shadow-lg inline-block">
              <h3 className="text-2xl font-bold text-gray-900 mb-4">
                加入满意用户的行列
              </h3>
              <p className="text-gray-600 mb-6">
                立即体验麒麟虚拟化，成为下一个成功案例
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                  免费试用 30 天
                </button>
                <button className="border border-gray-300 hover:border-gray-400 text-gray-700 px-6 py-3 rounded-lg font-medium transition-colors">
                  预约演示
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}