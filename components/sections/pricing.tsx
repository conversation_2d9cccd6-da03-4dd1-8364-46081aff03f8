import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { productVersions } from '@/lib/data/products';
import { CheckCircle, X } from 'lucide-react';
import Link from 'next/link';

export default function Pricing() {
  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="text-center mb-16">
            <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-200 mb-4">
              产品版本
            </Badge>
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              选择适合您的版本
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              从个人用户到企业级部署，我们提供完整的解决方案满足不同需求
            </p>
          </div>

          {/* Pricing Cards */}
          <div className="grid lg:grid-cols-3 gap-8 mb-16">
            {productVersions.map((version) => (
              <Card 
                key={version.id} 
                className={`relative ${version.popular ? 'border-blue-500 border-2' : 'border-gray-200'}`}
              >
                {version.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <Badge className="bg-blue-600 text-white">推荐</Badge>
                  </div>
                )}
                
                <CardHeader className="text-center pb-6">
                  <CardTitle className="text-2xl">{version.name}</CardTitle>
                  <CardDescription className="text-gray-600 mb-4">
                    {version.description}
                  </CardDescription>
                  <div className="space-y-2">
                    <div className="text-4xl font-bold text-gray-900">
                      ¥{version.price.monthly}
                      <span className="text-lg font-normal text-gray-600">/月</span>
                    </div>
                    <div className="text-sm text-gray-500">
                      年付 ¥{version.price.yearly} (省 ¥{version.price.monthly * 12 - version.price.yearly})
                    </div>
                  </div>
                </CardHeader>

                <CardContent className="space-y-6">
                  <div className="text-sm text-gray-600">
                    <strong>适合：</strong>{version.targetAudience}
                  </div>
                  
                  <div className="space-y-3">
                    {version.features.map((feature, index) => (
                      <div key={index} className="flex items-start space-x-3">
                        {feature.included ? (
                          <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5" />
                        ) : (
                          <X className="w-5 h-5 text-gray-400 flex-shrink-0 mt-0.5" />
                        )}
                        <div className={feature.included ? 'text-gray-900' : 'text-gray-400'}>
                          <div className="font-medium">{feature.name}</div>
                          <div className="text-sm">{feature.description}</div>
                        </div>
                      </div>
                    ))}
                  </div>

                  <div className="pt-6 space-y-3">
                    <Button 
                      className={`w-full ${version.popular ? 'bg-blue-600 hover:bg-blue-700' : 'bg-gray-900 hover:bg-gray-800'}`}
                      asChild
                    >
                      <Link href={`/products/${version.id}`}>
                        选择{version.name}
                      </Link>
                    </Button>
                    <Button variant="outline" className="w-full" asChild>
                      <Link href="/download">
                        免费试用
                      </Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Enterprise Contact */}
          <div className="bg-blue-600 rounded-2xl p-8 lg:p-12 text-center text-white">
            <h3 className="text-2xl font-bold mb-4">
              需要企业级定制方案？
            </h3>
            <p className="text-blue-100 mb-6 max-w-2xl mx-auto">
              我们为大型企业和政府机构提供定制化的虚拟化解决方案，
              包括私有化部署、定制开发、专业培训等服务。
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button variant="secondary" asChild>
                <Link href="/contact">
                  联系销售顾问
                </Link>
              </Button>
              <Button variant="outline" className="border-white text-white hover:bg-white hover:text-blue-600" asChild>
                <Link href="/demo">
                  预约演示
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}