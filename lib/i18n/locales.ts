export const locales = ['zh-CN', 'en-US'] as const;

export type Locale = (typeof locales)[number];

export const defaultLocale: Locale = 'zh-CN';

export const localeNames: Record<Locale, string> = {
  'zh-CN': '简体中文',
  'en-US': 'English',
};

export const localeDetection = {
  // Detect locale from browser settings
  detectFromBrowser: true,
  // Cookie name to store locale preference
  cookieName: 'locale',
  // URL parameter name for locale switching
  paramName: 'lang',
};

// Common translations that might be needed
export const commonTranslations = {
  'zh-CN': {
    // Navigation
    'nav.products': '产品',
    'nav.solutions': '解决方案', 
    'nav.support': '技术支持',
    'nav.customers': '客户案例',
    'nav.partners': '合作伙伴',
    'nav.download': '免费试用',
    'nav.purchase': '立即购买',
    
    // Common buttons and actions
    'btn.download': '下载',
    'btn.try_free': '免费试用',
    'btn.buy_now': '立即购买',
    'btn.learn_more': '了解更多',
    'btn.contact_us': '联系我们',
    'btn.get_started': '开始使用',
    
    // Common phrases
    'common.loading': '加载中...',
    'common.error': '出错了',
    'common.success': '成功',
    'common.required': '必填项',
    'common.optional': '可选',
    
    // Product related
    'product.personal': '个人版',
    'product.professional': '专业版', 
    'product.enterprise': '企业版',
    'product.free_trial': '免费试用',
    'product.price_monthly': '每月',
    'product.price_yearly': '每年',
    
    // Footer
    'footer.company': '麒麟虚拟化科技有限公司',
    'footer.rights_reserved': '版权所有',
  },
  'en-US': {
    // Navigation
    'nav.products': 'Products',
    'nav.solutions': 'Solutions',
    'nav.support': 'Support',
    'nav.customers': 'Customers',
    'nav.partners': 'Partners',
    'nav.download': 'Free Trial',
    'nav.purchase': 'Buy Now',
    
    // Common buttons and actions
    'btn.download': 'Download',
    'btn.try_free': 'Try Free',
    'btn.buy_now': 'Buy Now',
    'btn.learn_more': 'Learn More',
    'btn.contact_us': 'Contact Us',
    'btn.get_started': 'Get Started',
    
    // Common phrases
    'common.loading': 'Loading...',
    'common.error': 'Error occurred',
    'common.success': 'Success',
    'common.required': 'Required',
    'common.optional': 'Optional',
    
    // Product related
    'product.personal': 'Personal',
    'product.professional': 'Professional',
    'product.enterprise': 'Enterprise',
    'product.free_trial': 'Free Trial',
    'product.price_monthly': 'per month',
    'product.price_yearly': 'per year',
    
    // Footer
    'footer.company': 'Kylin Virtualization Technology Co., Ltd.',
    'footer.rights_reserved': 'All rights reserved',
  },
} as const;