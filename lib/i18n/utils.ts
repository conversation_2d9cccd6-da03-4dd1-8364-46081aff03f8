import { type Locale, defaultLocale, commonTranslations } from './locales';

// Get translation for a given key
export function getTranslation(locale: Locale, key: string): string {
  const translations = commonTranslations[locale] || commonTranslations[defaultLocale];
  return translations[key as keyof typeof translations] || key;
}

// Format currency based on locale
export function formatCurrency(amount: number, locale: Locale): string {
  if (locale === 'zh-CN') {
    return `¥${amount}`;
  }
  return `$${amount}`;
}

// Format date based on locale
export function formatDate(date: Date, locale: Locale): string {
  return date.toLocaleDateString(locale === 'zh-CN' ? 'zh-CN' : 'en-US');
}

// Get text direction (for future Arabic/Hebrew support)
export function getTextDirection(locale: Locale): 'ltr' | 'rtl' {
  // All currently supported locales are LTR
  return 'ltr';
}

// Chinese text optimizations
export function optimizeChineseText(text: string): string {
  // Add zero-width spaces for better line breaking in Chinese
  return text.replace(/([。！？；：，、])/g, '$1\u200B');
}

// Check if locale uses Chinese characters
export function isChineseLocale(locale: Locale): boolean {
  return locale.startsWith('zh');
}

// Get locale-specific font classes
export function getLocaleFontClasses(locale: Locale): string {
  if (isChineseLocale(locale)) {
    return 'font-chinese text-rendering-optimizeLegibility';
  }
  return 'font-latin';
}

// Number formatting based on locale
export function formatNumber(num: number, locale: Locale): string {
  if (locale === 'zh-CN') {
    // Chinese number formatting
    if (num >= 10000) {
      return `${(num / 10000).toFixed(1)}万`;
    }
    if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}千`;
    }
    return num.toString();
  }
  
  // English number formatting
  return num.toLocaleString('en-US');
}

// Get appropriate placeholder service URL
export function getPlaceholderImageUrl(width: number, height: number, locale: Locale): string {
  // Could customize placeholder images based on locale
  return `/api/placeholder/${width}/${height}`;
}

// Locale-specific SEO optimizations
export function getLocaleSEOConfig(locale: Locale) {
  if (locale === 'zh-CN') {
    return {
      language: 'zh-CN',
      region: 'CN',
      searchEngines: ['baidu', 'sogou', 'bing', 'google'],
      socialPlatforms: ['wechat', 'weibo', 'douyin'],
    };
  }
  
  return {
    language: 'en-US',
    region: 'US',
    searchEngines: ['google', 'bing', 'yahoo'],
    socialPlatforms: ['facebook', 'twitter', 'linkedin'],
  };
}