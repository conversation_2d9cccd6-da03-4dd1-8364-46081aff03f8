export interface ProductFeature {
  name: string;
  description: string;
  included: boolean;
}

export interface ProductVersion {
  id: string;
  name: string;
  description: string;
  price: {
    monthly: number;
    yearly: number;
  };
  features: ProductFeature[];
  popular?: boolean;
  targetAudience: string;
}

export const productVersions: ProductVersion[] = [
  {
    id: 'personal',
    name: '个人版',
    description: '适合个人用户和小型团队使用',
    price: {
      monthly: 99,
      yearly: 999
    },
    targetAudience: '个人用户、学生、小型工作室',
    features: [
      { name: '基础虚拟化功能', description: '完整的Windows虚拟化体验', included: true },
      { name: '麒麟OS深度优化', description: '针对麒麟操作系统优化', included: true },
      { name: '文件共享', description: '主机与虚拟机文件无缝共享', included: true },
      { name: '硬件加速', description: 'GPU加速支持', included: true },
      { name: '快照备份', description: '虚拟机快照和恢复', included: true },
      { name: '技术支持', description: '社区论坛支持', included: true },
      { name: '商业授权', description: '商业使用许可', included: false },
      { name: '高级管理功能', description: '批量部署和管理', included: false },
      { name: '专业技术支持', description: '7x24小时技术支持', included: false }
    ]
  },
  {
    id: 'professional',
    name: '专业版',
    description: '适合专业开发者和中小企业',
    price: {
      monthly: 299,
      yearly: 2999
    },
    popular: true,
    targetAudience: '专业开发者、中小企业、技术团队',
    features: [
      { name: '基础虚拟化功能', description: '完整的Windows虚拟化体验', included: true },
      { name: '麒麟OS深度优化', description: '针对麒麟操作系统优化', included: true },
      { name: '文件共享', description: '主机与虚拟机文件无缝共享', included: true },
      { name: '硬件加速', description: 'GPU加速支持', included: true },
      { name: '快照备份', description: '虚拟机快照和恢复', included: true },
      { name: '技术支持', description: '工作时间专业技术支持', included: true },
      { name: '商业授权', description: '商业使用许可', included: true },
      { name: '高级管理功能', description: '批量部署和管理', included: true },
      { name: '专业技术支持', description: '7x24小时技术支持', included: false }
    ]
  },
  {
    id: 'enterprise',
    name: '企业版',
    description: '适合大型企业和政府机构',
    price: {
      monthly: 599,
      yearly: 5999
    },
    targetAudience: '大型企业、政府机构、教育机构',
    features: [
      { name: '基础虚拟化功能', description: '完整的Windows虚拟化体验', included: true },
      { name: '麒麟OS深度优化', description: '针对麒麟操作系统优化', included: true },
      { name: '文件共享', description: '主机与虚拟机文件无缝共享', included: true },
      { name: '硬件加速', description: 'GPU加速支持', included: true },
      { name: '快照备份', description: '虚拟机快照和恢复', included: true },
      { name: '技术支持', description: '7x24小时专业技术支持', included: true },
      { name: '商业授权', description: '商业使用许可', included: true },
      { name: '高级管理功能', description: '批量部署和管理', included: true },
      { name: '专业技术支持', description: '7x24小时技术支持', included: true }
    ]
  }
];

export const coreFeatures = [
  {
    title: '麒麟OS深度集成',
    description: '专为麒麟操作系统优化，提供原生级别的性能和稳定性',
    icon: '🔧',
    benefits: [
      '系统级优化，性能提升30%',
      '完美兼容麒麟OS各个版本',
      '国产化硬件完全支持'
    ]
  },
  {
    title: '无缝Windows体验',
    description: '在麒麟OS上运行Windows应用程序，如同在原生系统中使用',
    icon: '🪟',
    benefits: [
      '一键启动Windows应用',
      '文件拖拽无缝共享',
      '剪贴板同步支持'
    ]
  },
  {
    title: '企业级安全',
    description: '符合国家信息安全标准，为企业提供可靠的安全保障',
    icon: '🔒',
    benefits: [
      '数据加密传输',
      '访问权限控制',
      '审计日志记录'
    ]
  },
  {
    title: '高性能虚拟化',
    description: '采用先进的虚拟化技术，提供接近原生的性能体验',
    icon: '⚡',
    benefits: [
      'GPU硬件加速',
      'CPU性能优化',
      '内存智能管理'
    ]
  }
];