# Quintar - 麒麟OS虚拟化产品官网 🚀

<div align="center">

![Next.js](https://img.shields.io/badge/Next.js-15.2.4-black?style=for-the-badge&logo=next.js)
![TypeScript](https://img.shields.io/badge/TypeScript-5.0-blue?style=for-the-badge&logo=typescript)
![Tailwind CSS](https://img.shields.io/badge/Tailwind_CSS-3.4-38B2AC?style=for-the-badge&logo=tailwind-css)
![React](https://img.shields.io/badge/React-19-61DAFB?style=for-the-badge&logo=react)

Quintar - 为麒麟操作系统虚拟化软件打造的现代化、专业级产品官网

[🌐 在线演示](https://your-domain.com) · [📖 部署文档](./DEPLOYMENT.md) · [🐛 问题反馈](https://github.com/your-repo/issues)

</div>

## 📋 目录

- [项目介绍](#-项目介绍)
- [功能特性](#-功能特性)
- [技术栈](#-技术栈)
- [项目结构](#-项目结构)
- [快速开始](#-快速开始)
- [开发指南](#-开发指南)
- [部署说明](#-部署说明)
- [贡献指南](#-贡献指南)
- [许可证](#-许可证)

## 🌟 项目介绍

本项目是Quintar虚拟化软件的官方产品网站，类似于 [Parallels](https://www.parallels.cn) 的功能和设计理念。网站展示了在麒麟操作系统上运行Windows软件的虚拟化产品，为用户提供完整的产品信息、技术支持和购买体验。

### 🎯 项目目标

- 🏢 **专业形象**: 展示企业级虚拟化解决方案的专业性
- 🎨 **现代设计**: 采用现代化的UI/UX设计，提供优秀的用户体验
- 📱 **移动优先**: 完全响应式设计，完美适配各种设备
- 🚀 **性能优化**: 极致的页面加载速度和用户交互体验
- 🔍 **SEO友好**: 全面的搜索引擎优化，提升网站曝光度

## ✨ 功能特性

### 🏠 核心页面
- **首页**: Hero区域、产品特性、定价预览、用户评价
- **产品页面**: 个人版、专业版、企业版详细介绍
- **麒麟OS专页**: 系统适配特色功能和技术优势
- **下载中心**: 免费试用注册和系统要求说明
- **购买页面**: 产品选择、定价方案和购买流程

### 🛠️ 支持服务
- **技术支持中心**: 
  - 常见问题解答 (FAQ)
  - 技术文档库
  - 在线客服系统
- **客户案例**: 成功案例展示、用户评价、统计数据
- **合作伙伴**: 合作伙伴计划、申请流程、权益说明

### 🎨 用户体验
- **响应式设计**: 支持桌面、平板、手机全平台
- **交互动画**: 流畅的页面过渡和微交互效果
- **主题支持**: 明暗主题切换（待扩展）
- **国际化**: 中文优化，支持多语言扩展

### 🔧 技术特性
- **性能优化**: 代码分割、图片懒加载、缓存策略
- **SEO优化**: Meta标签、结构化数据、站点地图
- **安全特性**: CSP头部、XSS保护、CSRF防护
- **监控告警**: 健康检查API、错误追踪、性能监控

## 🛠️ 技术栈

### 前端核心
- **[Next.js 15](https://nextjs.org/)** - React 全栈框架，支持 App Router
- **[TypeScript](https://www.typescriptlang.org/)** - 类型安全的 JavaScript 超集
- **[React 19](https://react.dev/)** - 用户界面构建库
- **[Tailwind CSS 3](https://tailwindcss.com/)** - 实用优先的 CSS 框架

### UI组件库
- **[Shadcn/ui](https://ui.shadcn.com/)** - 现代化的组件库
- **[Radix UI](https://www.radix-ui.com/)** - 无头组件基础库
- **[Lucide React](https://lucide.dev/)** - 美观的图标库
- **[Class Variance Authority](https://cva.style/)** - 组件变体管理

### 表单与数据
- **[React Hook Form](https://react-hook-form.com/)** - 高性能表单库
- **[Zod](https://zod.dev/)** - TypeScript优先的数据验证
- **[Date-fns](https://date-fns.org/)** - 现代JavaScript日期工具库

### 开发工具
- **[ESLint](https://eslint.org/)** - 代码质量检查
- **[Jest](https://jestjs.io/)** - 单元测试框架
- **[Testing Library](https://testing-library.com/)** - React组件测试
- **[Husky](https://typicode.github.io/husky/)** - Git钩子管理

### 部署与监控
- **[Docker](https://www.docker.com/)** - 容器化部署
- **[GitHub Actions](https://github.com/features/actions)** - CI/CD自动化
- **[Lighthouse](https://developers.google.com/web/tools/lighthouse)** - 性能审计

## 📁 项目结构

```
quintar/
├── 📁 app/                          # Next.js App Router
│   ├── 📄 layout.tsx               # 全局布局
│   ├── 📄 page.tsx                 # 首页
│   ├── 📄 loading.tsx              # 加载组件
│   ├── 📄 not-found.tsx           # 404页面
│   ├── 📄 sitemap.ts              # 站点地图
│   ├── 📄 robots.ts               # 搜索引擎规则
│   ├── 📁 api/                     # API路由
│   │   └── 📁 health/             # 健康检查
│   ├── 📁 products/                # 产品页面
│   │   ├── 📄 personal/           # 个人版
│   │   ├── 📄 professional/       # 专业版
│   │   └── 📄 enterprise/         # 企业版
│   ├── 📁 support/                 # 支持中心
│   │   ├── 📄 faq/                # 常见问题
│   │   ├── 📄 docs/               # 文档中心
│   │   └── 📄 contact/            # 联系我们
│   ├── 📄 kylin-os/               # 麒麟OS专页
│   ├── 📄 download/               # 下载中心
│   ├── 📄 purchase/               # 购买页面
│   ├── 📄 customers/              # 客户案例
│   └── 📄 partners/               # 合作伙伴
├── 📁 components/                   # React组件
│   ├── 📁 ui/                      # 基础UI组件
│   ├── 📁 layout/                  # 布局组件
│   │   ├── 📄 navigation.tsx      # 导航栏
│   │   └── 📄 footer.tsx          # 页脚
│   └── 📁 sections/                # 页面区块组件
│       ├── 📄 hero.tsx            # Hero区域
│       ├── 📄 features.tsx        # 功能特性
│       ├── 📄 pricing-preview.tsx # 定价预览
│       └── 📄 testimonials.tsx    # 用户评价
├── 📁 lib/                         # 工具库
│   ├── 📄 utils.ts                # 通用工具函数
│   ├── 📄 constants.ts            # 常量定义
│   └── 📁 i18n/                   # 国际化配置
├── 📁 __tests__/                   # 测试文件
│   ├── 📁 components/             # 组件测试
│   └── 📁 app/                    # 页面测试
├── 📁 .github/                     # GitHub配置
│   └── 📁 workflows/              # CI/CD工作流
├── 📁 scripts/                     # 构建脚本
├── 📄 Dockerfile                   # Docker配置
├── 📄 docker-compose.yml          # Docker Compose
├── 📄 next.config.mjs             # Next.js配置
├── 📄 tailwind.config.ts          # Tailwind配置
├── 📄 jest.config.js              # Jest测试配置
├── 📄 lighthouserc.js             # Lighthouse配置
└── 📄 DEPLOYMENT.md               # 部署文档
```

## 🚀 快速开始

### 环境要求
- **Node.js** >= 18.0.0
- **npm** >= 8.0.0 或 **yarn** >= 1.22.0
- **Git** >= 2.20.0

### 安装步骤

1. **克隆项目**
   ```bash
   git clone https://github.com/your-username/quintar.git
   cd quintar
   ```

2. **安装依赖**
   ```bash
   npm install
   # 或使用 yarn
   yarn install
   ```

3. **启动开发服务器**
   ```bash
   npm run dev
   # 或使用 yarn
   yarn dev
   ```

4. **访问应用**
   
   打开浏览器访问 [http://localhost:3000](http://localhost:3000)

### 环境配置

创建 `.env.local` 文件配置环境变量：

```env
# 应用配置
NODE_ENV=development
PORT=3000

# 网站配置
NEXT_PUBLIC_SITE_URL=http://localhost:3000
NEXT_PUBLIC_SITE_NAME="Quintar"

# 分析工具 (可选)
NEXT_PUBLIC_GA_ID=your_google_analytics_id

# 第三方服务 (可选)
RESEND_API_KEY=your_resend_api_key
DATABASE_URL=your_database_url
```

## 💻 开发指南

### 可用脚本

```bash
# 开发服务器
npm run dev          # 启动开发环境 (http://localhost:3000)

# 构建和测试
npm run build        # 构建生产版本
npm run start        # 启动生产服务器
npm run lint         # 代码检查
npm run lint:fix     # 自动修复代码问题

# 测试
npm run test         # 运行单元测试
npm run test:watch   # 监视模式运行测试
npm run test:coverage # 生成测试覆盖率报告

# 类型检查
npx tsc --noEmit     # TypeScript类型检查
```

### 代码规范

项目采用以下代码规范：

- **[ESLint](https://eslint.org/)**: JavaScript/TypeScript代码检查
- **[Prettier](https://prettier.io/)**: 代码格式化
- **[TypeScript](https://www.typescriptlang.org/)**: 类型安全
- **[Conventional Commits](https://www.conventionalcommits.org/)**: 提交信息规范

### 组件开发

1. **创建新组件**
   ```bash
   # 在 components/ui/ 创建基础组件
   # 在 components/sections/ 创建页面区块组件
   ```

2. **组件模板**
   ```tsx
   import { cn } from '@/lib/utils'
   
   interface ComponentProps {
     className?: string
     children?: React.ReactNode
   }
   
   export function Component({ className, children, ...props }: ComponentProps) {
     return (
       <div className={cn('default-classes', className)} {...props}>
         {children}
       </div>
     )
   }
   ```

3. **添加测试**
   ```tsx
   import { render, screen } from '@testing-library/react'
   import { Component } from './component'
   
   describe('Component', () => {
     it('renders correctly', () => {
       render(<Component>Test content</Component>)
       expect(screen.getByText('Test content')).toBeInTheDocument()
     })
   })
   ```

### 样式指南

使用 Tailwind CSS 进行样式开发：

```tsx
// 推荐: 使用 Tailwind 类名
<div className="flex items-center justify-between p-4 bg-white dark:bg-gray-800">

// 条件样式: 使用 cn() 工具函数
<button className={cn(
  "px-4 py-2 rounded-md transition-colors",
  variant === "primary" && "bg-blue-600 text-white",
  variant === "secondary" && "bg-gray-200 text-gray-900"
)}>

// 响应式设计
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
```

## 🚀 部署说明

### 本地构建测试

```bash
# 构建项目
npm run build

# 启动生产服务器
npm start

# 访问 http://localhost:3000
```

### Docker 部署

```bash
# 使用 Docker Compose (推荐)
docker-compose up -d

# 或使用 Dockerfile
docker build -t quintar .
docker run -p 3000:3000 quintar
```

### 云平台部署

#### Vercel 部署 (推荐)
[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https://github.com/your-username/quintar)

#### Netlify 部署
[![Deploy to Netlify](https://www.netlify.com/img/deploy/button.svg)](https://app.netlify.com/start/deploy?repository=https://github.com/your-username/quintar)

详细部署说明请参考 [DEPLOYMENT.md](./DEPLOYMENT.md)

## 🧪 测试说明

### 测试框架
- **Jest**: 单元测试框架
- **React Testing Library**: React组件测试
- **Lighthouse CI**: 性能和质量测试

### 运行测试

```bash
# 运行所有测试
npm test

# 监视模式
npm run test:watch

# 生成覆盖率报告
npm run test:coverage

# 运行 Lighthouse 审计
npx lhci autorun
```

### 测试覆盖率

项目目标测试覆盖率：
- **语句覆盖率**: >= 80%
- **分支覆盖率**: >= 75%
- **函数覆盖率**: >= 80%
- **行覆盖率**: >= 80%

## 🤝 贡献指南

我们欢迎所有形式的贡献！请阅读以下指南：

### 提交流程

1. **Fork 项目**
2. **创建特性分支** (`git checkout -b feature/AmazingFeature`)
3. **提交更改** (`git commit -m 'Add some AmazingFeature'`)
4. **推送分支** (`git push origin feature/AmazingFeature`)
5. **提交 Pull Request**

### 提交信息规范

使用 [Conventional Commits](https://www.conventionalcommits.org/) 格式：

```
type(scope): description

feat(components): add new hero section
fix(api): resolve health check endpoint issue
docs(readme): update installation instructions
style(ui): improve button component styling
refactor(utils): optimize image processing function
test(components): add hero section tests
```

### 代码审查

所有 Pull Request 都需要通过：
- ✅ 自动化测试
- ✅ 代码质量检查
- ✅ 至少一位维护者的审查

## 📊 性能指标

### Lighthouse 评分目标
- **性能 (Performance)**: >= 90
- **可访问性 (Accessibility)**: >= 95
- **最佳实践 (Best Practices)**: >= 90
- **SEO**: >= 95

### 核心Web指标
- **LCP (Largest Contentful Paint)**: < 2.5s
- **FID (First Input Delay)**: < 100ms
- **CLS (Cumulative Layout Shift)**: < 0.1

## 📈 路线图

### 近期计划 (Q1 2024)
- [ ] 添加产品视频演示
- [ ] 集成在线客服系统
- [ ] 完善移动端用户体验
- [ ] 添加更多语言支持

### 中期计划 (Q2-Q3 2024)
- [ ] 用户账户系统
- [ ] 产品试用追踪
- [ ] A/B测试框架
- [ ] 高级分析和报告

### 长期计划 (Q4 2024+)
- [ ] 微服务架构重构
- [ ] PWA支持
- [ ] 离线功能
- [ ] AI客服集成

## 🆘 常见问题

<details>
<summary><strong>Q: 如何添加新页面？</strong></summary>

在 `app/` 目录下创建新文件夹和 `page.tsx` 文件：

```tsx
// app/new-page/page.tsx
export default function NewPage() {
  return (
    <div>
      <h1>新页面</h1>
    </div>
  )
}
```
</details>

<details>
<summary><strong>Q: 如何修改主题颜色？</strong></summary>

编辑 `tailwind.config.ts` 文件中的颜色配置：

```ts
module.exports = {
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#eff6ff',
          500: '#3b82f6',
          900: '#1e3a8a',
        },
      },
    },
  },
}
```
</details>

<details>
<summary><strong>Q: 如何配置SEO？</strong></summary>

在页面文件中导出 `metadata` 对象：

```tsx
import type { Metadata } from 'next'

export const metadata: Metadata = {
  title: '页面标题',
  description: '页面描述',
  keywords: '关键词1, 关键词2',
}
```
</details>

## 📞 联系我们

- **项目维护者**: [Your Name](mailto:<EMAIL>)
- **问题反馈**: [GitHub Issues](https://github.com/your-username/kylin-virtualization-website/issues)
- **功能建议**: [GitHub Discussions](https://github.com/your-username/kylin-virtualization-website/discussions)

## 📄 许可证

本项目采用 [MIT 许可证](LICENSE) - 详细信息请查看 LICENSE 文件。

---

<div align="center">

**[⬆ 回到顶部](#麒麟os虚拟化产品官网-)**

Made with ❤️ by [Your Team Name](https://github.com/your-username)

</div>