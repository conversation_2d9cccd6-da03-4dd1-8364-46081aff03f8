import { render, screen } from '@testing-library/react'
import { Hero } from '@/components/sections/hero'

describe('Hero Component', () => {
  it('renders hero section with correct content', () => {
    render(<Hero />)
    
    // Check if main heading is present
    expect(screen.getByText(/麒麟操作系统上的Windows虚拟化解决方案/)).toBeInTheDocument()
    
    // Check if subheading is present
    expect(screen.getByText(/在麒麟操作系统上无缝运行Windows应用程序/)).toBeInTheDocument()
    
    // Check if buttons are present
    expect(screen.getByText('免费试用')).toBeInTheDocument()
    expect(screen.getByText('了解更多')).toBeInTheDocument()
  })
  
  it('renders hero image with proper attributes', () => {
    render(<Hero />)
    
    const heroImage = screen.getByAltText('麒麟OS虚拟化演示')
    expect(heroImage).toBeInTheDocument()
    expect(heroImage).toHaveAttribute('src')
  })
  
  it('has proper responsive classes', () => {
    const { container } = render(<Hero />)
    const heroSection = container.querySelector('section')
    
    expect(heroSection).toHaveClass('relative', 'min-h-screen', 'flex', 'items-center', 'justify-center')
  })
})