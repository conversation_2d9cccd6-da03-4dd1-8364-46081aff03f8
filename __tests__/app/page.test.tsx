import { render, screen } from '@testing-library/react'
import Page from '@/app/page'

// Mock the Hero component
jest.mock('@/components/sections/hero', () => ({
  Hero: () => <div data-testid="hero-section">Hero Section</div>
}))

// Mock the Features component
jest.mock('@/components/sections/features', () => ({
  Features: () => <div data-testid="features-section">Features Section</div>
}))

// Mock the PricingPreview component
jest.mock('@/components/sections/pricing-preview', () => ({
  PricingPreview: () => <div data-testid="pricing-preview-section">Pricing Preview Section</div>
}))

// Mock the Testimonials component
jest.mock('@/components/sections/testimonials', () => ({
  Testimonials: () => <div data-testid="testimonials-section">Testimonials Section</div>
}))

describe('Home Page', () => {
  it('renders without crashing', () => {
    render(<Page />)
  })
  
  it('renders all main sections', () => {
    render(<Page />)
    
    // Check if all sections are present
    expect(screen.getByTestId('hero-section')).toBeInTheDocument()
    expect(screen.getByTestId('features-section')).toBeInTheDocument()
    expect(screen.getByTestId('pricing-preview-section')).toBeInTheDocument()
    expect(screen.getByTestId('testimonials-section')).toBeInTheDocument()
  })
  
  it('has proper page structure', () => {
    const { container } = render(<Page />)
    const mainElement = container.querySelector('main')
    
    expect(mainElement).toBeInTheDocument()
    expect(mainElement?.children).toHaveLength(4) // Hero, Features, Pricing, Testimonials
  })
})