import { Metadata } from 'next';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { productVersions } from '@/lib/data/products';
import { CheckCircle, Download, ShoppingCart, Code, Server, Headphones, Star } from 'lucide-react';

export const metadata: Metadata = {
  title: '专业版 - 麒麟虚拟化',
  description: '麒麟虚拟化专业版，适合专业开发者和中小企业使用，提供高级管理功能和商业授权。',
  keywords: '麒麟虚拟化,专业版,开发者,中小企业,商业授权,高级功能'
};

const professionalVersion = productVersions.find(v => v.id === 'professional')!;

export default function ProfessionalPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <section className="bg-gradient-to-r from-green-600 to-emerald-600 text-white">
        <div className="container mx-auto px-4 py-20">
          <div className="max-w-4xl mx-auto text-center">
            <div className="flex items-center justify-center mb-4">
              <Badge className="bg-white/20 text-white hover:bg-white/30 mr-2">
                专业版
              </Badge>
              <Badge className="bg-yellow-500 text-white">
                <Star className="w-3 h-3 mr-1" />
                推荐
              </Badge>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              {professionalVersion.name}
            </h1>
            <p className="text-xl text-green-100 mb-8">
              {professionalVersion.description}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="bg-white text-green-600 hover:bg-gray-100">
                <ShoppingCart className="mr-2 h-4 w-4" />
                立即购买 ¥{professionalVersion.price.monthly}/月
              </Button>
              <Button size="lg" variant="outline" className="border-white text-white hover:bg-white/10">
                <Download className="mr-2 h-4 w-4" />
                免费试用 30 天
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Why Professional */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                为什么选择专业版？
              </h2>
              <p className="text-xl text-gray-600">
                专业版提供更多高级功能，满足专业开发者和企业用户需求
              </p>
            </div>

            <div className="grid md:grid-cols-3 gap-8 mb-16">
              <Card className="text-center border-2 border-green-200">
                <CardHeader>
                  <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Code className="w-8 h-8 text-green-600" />
                  </div>
                  <CardTitle>商业授权</CardTitle>
                  <CardDescription>
                    完整的商业使用许可，支持企业级应用部署
                  </CardDescription>
                </CardHeader>
              </Card>

              <Card className="text-center border-2 border-blue-200">
                <CardHeader>
                  <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Server className="w-8 h-8 text-blue-600" />
                  </div>
                  <CardTitle>高级管理</CardTitle>
                  <CardDescription>
                    批量部署和管理功能，提高工作效率
                  </CardDescription>
                </CardHeader>
              </Card>

              <Card className="text-center border-2 border-purple-200">
                <CardHeader>
                  <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Headphones className="w-8 h-8 text-purple-600" />
                  </div>
                  <CardTitle>专业支持</CardTitle>
                  <CardDescription>
                    工作时间专业技术支持，快速解决问题
                  </CardDescription>
                </CardHeader>
              </Card>
            </div>

            {/* Comparison with Personal */}
            <div className="bg-gray-50 rounded-lg p-8">
              <h3 className="text-2xl font-bold text-gray-900 mb-6 text-center">
                专业版 vs 个人版
              </h3>
              <div className="grid md:grid-cols-2 gap-8">
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 mb-4">专业版独有功能</h4>
                  <ul className="space-y-2">
                    <li className="flex items-center text-green-600">
                      <CheckCircle className="w-4 h-4 mr-2" />
                      商业使用许可
                    </li>
                    <li className="flex items-center text-green-600">
                      <CheckCircle className="w-4 h-4 mr-2" />
                      批量部署和管理
                    </li>
                    <li className="flex items-center text-green-600">
                      <CheckCircle className="w-4 h-4 mr-2" />
                      专业技术支持
                    </li>
                    <li className="flex items-center text-green-600">
                      <CheckCircle className="w-4 h-4 mr-2" />
                      高级网络配置
                    </li>
                  </ul>
                </div>
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 mb-4">共同功能</h4>
                  <ul className="space-y-2">
                    <li className="flex items-center text-gray-600">
                      <CheckCircle className="w-4 h-4 mr-2" />
                      基础虚拟化功能
                    </li>
                    <li className="flex items-center text-gray-600">
                      <CheckCircle className="w-4 h-4 mr-2" />
                      麒麟OS深度优化
                    </li>
                    <li className="flex items-center text-gray-600">
                      <CheckCircle className="w-4 h-4 mr-2" />
                      文件共享
                    </li>
                    <li className="flex items-center text-gray-600">
                      <CheckCircle className="w-4 h-4 mr-2" />
                      硬件加速
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Pricing */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                专业版定价
              </h2>
              <p className="text-gray-600">
                性价比最高的选择，适合中小企业和专业开发者
              </p>
            </div>

            <div className="grid md:grid-cols-2 gap-8">
              <Card>
                <CardHeader className="text-center">
                  <CardTitle className="text-2xl">月付</CardTitle>
                  <CardDescription>灵活付费，随时调整</CardDescription>
                  <div className="text-4xl font-bold text-green-600 mt-4">
                    ¥{professionalVersion.price.monthly}
                    <span className="text-lg font-normal text-gray-600">/月</span>
                  </div>
                </CardHeader>
                <CardContent>
                  <Button className="w-full bg-green-600 hover:bg-green-700">
                    选择月付方案
                  </Button>
                </CardContent>
              </Card>

              <Card className="border-2 border-green-500 relative">
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <Badge className="bg-green-600 text-white">最优惠</Badge>
                </div>
                <CardHeader className="text-center">
                  <CardTitle className="text-2xl">年付</CardTitle>
                  <CardDescription>年付享受超值折扣</CardDescription>
                  <div className="text-4xl font-bold text-green-600 mt-4">
                    ¥{professionalVersion.price.yearly}
                    <span className="text-lg font-normal text-gray-600">/年</span>
                  </div>
                  <div className="text-sm text-green-600 font-medium">
                    省 ¥{professionalVersion.price.monthly * 12 - professionalVersion.price.yearly}
                  </div>
                </CardHeader>
                <CardContent>
                  <Button className="w-full bg-green-600 hover:bg-green-700">
                    选择年付方案
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Target Audience */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                适合谁使用？
              </h2>
              <p className="text-xl text-gray-600">
                专业版为以下用户群体量身定制
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                {
                  title: '软件开发公司',
                  description: '需要在麒麟OS上开发和测试Windows应用的团队',
                  icon: '💻',
                  benefits: ['商业使用许可', '团队协作功能', '专业技术支持']
                },
                {
                  title: '中小企业',
                  description: '需要在麒麟OS环境中运行Windows业务软件',
                  icon: '🏢',
                  benefits: ['批量部署', '集中管理', '成本控制']
                },
                {
                  title: '系统集成商',
                  description: '为客户提供麒麟OS虚拟化解决方案',
                  icon: '🔧',
                  benefits: ['技术支持', '定制服务', '合作伙伴计划']
                },
                {
                  title: '设计工作室',
                  description: '需要使用Windows专业设计软件的创意团队',
                  icon: '🎨',
                  benefits: ['高性能渲染', '文件共享', '版本管理']
                },
                {
                  title: '教育机构',
                  description: '需要在教学中使用Windows软件的学校',
                  icon: '🎓',
                  benefits: ['教育优惠', '批量授权', '技术培训']
                },
                {
                  title: '研发团队',
                  description: '需要跨平台开发和测试的研发机构',
                  icon: '🔬',
                  benefits: ['环境隔离', '快速部署', '团队协作']
                }
              ].map((audience, index) => (
                <Card key={index} className="h-full">
                  <CardHeader>
                    <div className="text-4xl mb-4">{audience.icon}</div>
                    <CardTitle className="text-lg">{audience.title}</CardTitle>
                    <CardDescription>{audience.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-1">
                      {audience.benefits.map((benefit, i) => (
                        <li key={i} className="flex items-center text-sm text-gray-600">
                          <CheckCircle className="w-3 h-3 mr-2 text-green-500" />
                          {benefit}
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* CTA */}
      <section className="py-20 bg-green-600 text-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl font-bold mb-4">
              升级到专业版，释放更多可能
            </h2>
            <p className="text-xl text-green-100 mb-8">
              30天免费试用，体验专业版的强大功能
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="bg-white text-green-600 hover:bg-gray-100">
                <Download className="mr-2 h-4 w-4" />
                免费试用 30 天
              </Button>
              <Button size="lg" variant="outline" className="border-white text-white hover:bg-white/10">
                <ShoppingCart className="mr-2 h-4 w-4" />
                立即购买
              </Button>
            </div>
            <p className="text-sm text-green-200 mt-4">
              已有个人版？联系我们获取升级优惠
            </p>
          </div>
        </div>
      </section>
    </div>
  );
}