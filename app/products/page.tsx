import { Metadata } from 'next';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { productVersions } from '@/lib/data/products';
import { CheckCircle, X, Download, ShoppingCart } from 'lucide-react';
import Link from 'next/link';

export const metadata: Metadata = {
  title: '产品版本 - 麒麟虚拟化',
  description: '选择适合您的麒麟虚拟化产品版本，包括个人版、专业版和企业版，满足不同用户需求。',
  keywords: '麒麟虚拟化,个人版,专业版,企业版,价格,功能对比'
};

export default function ProductsPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <section className="bg-white border-b">
        <div className="container mx-auto px-4 py-16">
          <div className="max-w-4xl mx-auto text-center">
            <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-200 mb-4">
              产品版本
            </Badge>
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              选择最适合您的版本
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              从个人用户到企业级部署，我们提供完整的解决方案满足不同需求
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" asChild>
                <Link href="/download">
                  <Download className="mr-2 h-4 w-4" />
                  免费试用 30 天
                </Link>
              </Button>
              <Button size="lg" variant="outline" asChild>
                <Link href="/contact">
                  联系销售顾问
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Pricing Comparison */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-7xl mx-auto">
            <Tabs defaultValue="comparison" className="w-full">
              <TabsList className="grid w-full grid-cols-2 max-w-md mx-auto mb-12">
                <TabsTrigger value="comparison">功能对比</TabsTrigger>
                <TabsTrigger value="cards">版本选择</TabsTrigger>
              </TabsList>
              
              <TabsContent value="cards">
                <div className="grid lg:grid-cols-3 gap-8">
                  {productVersions.map((version) => (
                    <Card 
                      key={version.id} 
                      className={`relative ${version.popular ? 'border-blue-500 border-2' : 'border-gray-200'}`}
                    >
                      {version.popular && (
                        <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                          <Badge className="bg-blue-600 text-white">推荐</Badge>
                        </div>
                      )}
                      
                      <CardHeader className="text-center pb-6">
                        <CardTitle className="text-2xl">{version.name}</CardTitle>
                        <CardDescription className="text-gray-600 mb-4">
                          {version.description}
                        </CardDescription>
                        <div className="space-y-2">
                          <div className="text-4xl font-bold text-gray-900">
                            ¥{version.price.monthly}
                            <span className="text-lg font-normal text-gray-600">/月</span>
                          </div>
                          <div className="text-sm text-gray-500">
                            年付 ¥{version.price.yearly} (省 ¥{version.price.monthly * 12 - version.price.yearly})
                          </div>
                        </div>
                      </CardHeader>

                      <CardContent className="space-y-6">
                        <div className="text-sm text-gray-600">
                          <strong>适合：</strong>{version.targetAudience}
                        </div>
                        
                        <div className="space-y-3">
                          {version.features.slice(0, 6).map((feature, index) => (
                            <div key={index} className="flex items-start space-x-3">
                              {feature.included ? (
                                <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5" />
                              ) : (
                                <X className="w-5 h-5 text-gray-400 flex-shrink-0 mt-0.5" />
                              )}
                              <div className={feature.included ? 'text-gray-900' : 'text-gray-400'}>
                                <div className="font-medium">{feature.name}</div>
                              </div>
                            </div>
                          ))}
                        </div>

                        <div className="pt-6 space-y-3">
                          <Button 
                            className={`w-full ${version.popular ? 'bg-blue-600 hover:bg-blue-700' : 'bg-gray-900 hover:bg-gray-800'}`}
                            asChild
                          >
                            <Link href={`/products/${version.id}`}>
                              <ShoppingCart className="mr-2 h-4 w-4" />
                              选择{version.name}
                            </Link>
                          </Button>
                          <Button variant="outline" className="w-full" asChild>
                            <Link href="/download">
                              <Download className="mr-2 h-4 w-4" />
                              免费试用
                            </Link>
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </TabsContent>

              <TabsContent value="comparison">
                <div className="bg-white rounded-lg shadow-lg overflow-hidden">
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">功能</th>
                          {productVersions.map((version) => (
                            <th key={version.id} className="px-6 py-4 text-center text-sm font-medium text-gray-900">
                              <div className="space-y-1">
                                <div>{version.name}</div>
                                <div className="text-2xl font-bold text-blue-600">
                                  ¥{version.price.monthly}
                                  <span className="text-sm font-normal text-gray-600">/月</span>
                                </div>
                              </div>
                            </th>
                          ))}
                        </tr>
                      </thead>
                      <tbody className="divide-y divide-gray-200">
                        {productVersions[0].features.map((_, featureIndex) => (
                          <tr key={featureIndex} className="hover:bg-gray-50">
                            <td className="px-6 py-4">
                              <div>
                                <div className="font-medium text-gray-900">
                                  {productVersions[0].features[featureIndex].name}
                                </div>
                                <div className="text-sm text-gray-500">
                                  {productVersions[0].features[featureIndex].description}
                                </div>
                              </div>
                            </td>
                            {productVersions.map((version) => (
                              <td key={version.id} className="px-6 py-4 text-center">
                                {version.features[featureIndex].included ? (
                                  <CheckCircle className="w-5 h-5 text-green-500 mx-auto" />
                                ) : (
                                  <X className="w-5 h-5 text-gray-400 mx-auto" />
                                )}
                              </td>
                            ))}
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </section>

      {/* FAQ */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold text-gray-900 mb-12 text-center">
              常见问题
            </h2>
            <div className="space-y-6">
              {[
                {
                  question: '可以随时升级版本吗？',
                  answer: '是的，您可以随时升级到更高级的版本。升级费用将按比例计算，您只需支付差价即可。'
                },
                {
                  question: '企业版支持多少用户？',
                  answer: '企业版默认支持100个用户，如需更多用户，可以联系我们的销售团队获取定制方案。'
                },
                {
                  question: '是否提供技术支持？',
                  answer: '所有版本都提供技术支持，个人版提供社区论坛支持，专业版和企业版提供更高级的技术支持服务。'
                },
                {
                  question: '可以申请试用吗？',
                  answer: '是的，我们为所有版本提供30天免费试用，无需信用卡，您可以充分体验产品功能。'
                }
              ].map((faq, index) => (
                <Card key={index}>
                  <CardHeader>
                    <CardTitle className="text-lg">{faq.question}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-600">{faq.answer}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}