import { Metadata } from 'next';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { productVersions } from '@/lib/data/products';
import { CheckCircle, Download, Building, Shield, Clock, Phone, Users, Settings } from 'lucide-react';

export const metadata: Metadata = {
  title: '企业版 - 麒麟虚拟化',
  description: '麒麟虚拟化企业版，适合大型企业和政府机构使用，提供7x24小时技术支持和企业级功能。',
  keywords: '麒麟虚拟化,企业版,大型企业,政府机构,企业级,7x24技术支持'
};

const enterpriseVersion = productVersions.find(v => v.id === 'enterprise')!;

export default function EnterprisePage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <section className="bg-gradient-to-r from-purple-600 to-indigo-600 text-white">
        <div className="container mx-auto px-4 py-20">
          <div className="max-w-4xl mx-auto text-center">
            <Badge className="bg-white/20 text-white hover:bg-white/30 mb-4">
              企业版
            </Badge>
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              {enterpriseVersion.name}
            </h1>
            <p className="text-xl text-purple-100 mb-8">
              {enterpriseVersion.description}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="bg-white text-purple-600 hover:bg-gray-100">
                <Phone className="mr-2 h-4 w-4" />
                联系销售顾问
              </Button>
              <Button size="lg" variant="outline" className="border-white text-white hover:bg-white/10">
                <Download className="mr-2 h-4 w-4" />
                预约演示
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Enterprise Features */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                企业级功能特性
              </h2>
              <p className="text-xl text-gray-600">
                为大型企业和政府机构量身定制的全面解决方案
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
              <Card className="text-center border-2 border-purple-200">
                <CardHeader>
                  <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Clock className="w-8 h-8 text-purple-600" />
                  </div>
                  <CardTitle>7x24技术支持</CardTitle>
                  <CardDescription>
                    全天候专业技术支持，确保业务连续性
                  </CardDescription>
                </CardHeader>
              </Card>

              <Card className="text-center border-2 border-blue-200">
                <CardHeader>
                  <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Shield className="w-8 h-8 text-blue-600" />
                  </div>
                  <CardTitle>企业级安全</CardTitle>
                  <CardDescription>
                    符合国家信息安全标准的高级安全功能
                  </CardDescription>
                </CardHeader>
              </Card>

              <Card className="text-center border-2 border-green-200">
                <CardHeader>
                  <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Users className="w-8 h-8 text-green-600" />
                  </div>
                  <CardTitle>批量管理</CardTitle>
                  <CardDescription>
                    支持大规模部署和集中管理
                  </CardDescription>
                </CardHeader>
              </Card>

              <Card className="text-center border-2 border-orange-200">
                <CardHeader>
                  <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Settings className="w-8 h-8 text-orange-600" />
                  </div>
                  <CardTitle>定制服务</CardTitle>
                  <CardDescription>
                    根据企业需求提供定制化解决方案
                  </CardDescription>
                </CardHeader>
              </Card>

              <Card className="text-center border-2 border-red-200">
                <CardHeader>
                  <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Building className="w-8 h-8 text-red-600" />
                  </div>
                  <CardTitle>合规认证</CardTitle>
                  <CardDescription>
                    通过多项国家级安全认证
                  </CardDescription>
                </CardHeader>
              </Card>

              <Card className="text-center border-2 border-indigo-200">
                <CardHeader>
                  <div className="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Phone className="w-8 h-8 text-indigo-600" />
                  </div>
                  <CardTitle>专属服务</CardTitle>
                  <CardDescription>
                    配备专属客户经理和技术专家
                  </CardDescription>
                </CardHeader>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Pricing */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                企业版投资
              </h2>
              <p className="text-gray-600">
                为企业级需求提供的全方位解决方案
              </p>
            </div>

            <div className="grid md:grid-cols-2 gap-8">
              <Card>
                <CardHeader className="text-center">
                  <CardTitle className="text-2xl">标准企业版</CardTitle>
                  <CardDescription>适合中等规模企业</CardDescription>
                  <div className="text-4xl font-bold text-purple-600 mt-4">
                    ¥{enterpriseVersion.price.monthly}
                    <span className="text-lg font-normal text-gray-600">/月</span>
                  </div>
                  <div className="text-sm text-gray-500">
                    支持100用户，年付优惠
                  </div>
                </CardHeader>
                <CardContent>
                  <Button className="w-full bg-purple-600 hover:bg-purple-700">
                    联系销售
                  </Button>
                </CardContent>
              </Card>

              <Card className="border-2 border-purple-500 relative">
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <Badge className="bg-purple-600 text-white">定制方案</Badge>
                </div>
                <CardHeader className="text-center">
                  <CardTitle className="text-2xl">定制企业版</CardTitle>
                  <CardDescription>大型企业和政府机构</CardDescription>
                  <div className="text-4xl font-bold text-purple-600 mt-4">
                    定制
                    <span className="text-lg font-normal text-gray-600">定价</span>
                  </div>
                  <div className="text-sm text-gray-500">
                    根据用户规模和需求定制
                  </div>
                </CardHeader>
                <CardContent>
                  <Button className="w-full bg-purple-600 hover:bg-purple-700">
                    预约咨询
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Target Organizations */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                适用机构
              </h2>
              <p className="text-xl text-gray-600">
                企业版专为以下机构设计
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                {
                  title: '政府机构',
                  description: '需要符合国家信息安全标准的政务系统',
                  icon: '🏛️',
                  features: ['安全合规', '审计追踪', '等保认证', '专属支持']
                },
                {
                  title: '大型企业',
                  description: '需要大规模部署的跨国公司和集团企业',
                  icon: '🏢',
                  features: ['批量部署', '集中管理', '成本控制', '技术支持']
                },
                {
                  title: '金融机构',
                  description: '对安全性要求极高的银行、证券、保险公司',
                  icon: '🏦',
                  features: ['金融级安全', '风险控制', '监管合规', '业务连续']
                },
                {
                  title: '教育机构',
                  description: '需要大规模教学应用的高校和科研院所',
                  icon: '🎓',
                  features: ['教育优惠', '批量授权', '实验环境', '技术培训']
                },
                {
                  title: '医疗机构',
                  description: '需要专业医疗软件的医院和医疗集团',
                  icon: '🏥',
                  features: ['医疗合规', '数据安全', '系统稳定', '应急响应']
                },
                {
                  title: '制造企业',
                  description: '需要工业软件的大型制造企业',
                  icon: '🏭',
                  features: ['工业软件', '生产管理', '质量控制', '技术支持']
                }
              ].map((org, index) => (
                <Card key={index} className="h-full">
                  <CardHeader>
                    <div className="text-4xl mb-4">{org.icon}</div>
                    <CardTitle className="text-lg">{org.title}</CardTitle>
                    <CardDescription>{org.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-1">
                      {org.features.map((feature, i) => (
                        <li key={i} className="flex items-center text-sm text-gray-600">
                          <CheckCircle className="w-3 h-3 mr-2 text-green-500" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Support & Services */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                企业级服务保障
              </h2>
              <p className="text-xl text-gray-600">
                全方位的服务支持，确保您的业务稳定运行
              </p>
            </div>

            <div className="grid md:grid-cols-2 gap-8">
              <Card className="p-8">
                <CardHeader>
                  <CardTitle className="text-xl mb-4">技术支持服务</CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-3">
                    <li className="flex items-center">
                      <CheckCircle className="w-5 h-5 mr-3 text-green-500" />
                      7x24小时技术支持热线
                    </li>
                    <li className="flex items-center">
                      <CheckCircle className="w-5 h-5 mr-3 text-green-500" />
                      专属技术支持工程师
                    </li>
                    <li className="flex items-center">
                      <CheckCircle className="w-5 h-5 mr-3 text-green-500" />
                      远程协助和现场服务
                    </li>
                    <li className="flex items-center">
                      <CheckCircle className="w-5 h-5 mr-3 text-green-500" />
                      定期健康检查和优化
                    </li>
                  </ul>
                </CardContent>
              </Card>

              <Card className="p-8">
                <CardHeader>
                  <CardTitle className="text-xl mb-4">实施与培训</CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-3">
                    <li className="flex items-center">
                      <CheckCircle className="w-5 h-5 mr-3 text-green-500" />
                      专业实施团队现场部署
                    </li>
                    <li className="flex items-center">
                      <CheckCircle className="w-5 h-5 mr-3 text-green-500" />
                      定制化培训方案
                    </li>
                    <li className="flex items-center">
                      <CheckCircle className="w-5 h-5 mr-3 text-green-500" />
                      用户操作手册和文档
                    </li>
                    <li className="flex items-center">
                      <CheckCircle className="w-5 h-5 mr-3 text-green-500" />
                      持续的技术咨询服务
                    </li>
                  </ul>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* CTA */}
      <section className="py-20 bg-purple-600 text-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl font-bold mb-4">
              让我们为您的企业定制解决方案
            </h2>
            <p className="text-xl text-purple-100 mb-8">
              专业的销售顾问将为您提供详细的产品介绍和定制方案
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="bg-white text-purple-600 hover:bg-gray-100">
                <Phone className="mr-2 h-4 w-4" />
                联系销售顾问
              </Button>
              <Button size="lg" variant="outline" className="border-white text-white hover:bg-white/10">
                <Download className="mr-2 h-4 w-4" />
                预约产品演示
              </Button>
            </div>
            <div className="mt-8 text-sm text-purple-200">
              <p>📞 销售热线: ************</p>
              <p>📧 企业邮箱: <EMAIL></p>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}