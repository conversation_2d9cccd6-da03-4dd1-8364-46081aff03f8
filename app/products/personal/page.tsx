import { Metadata } from 'next';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { productVersions } from '@/lib/data/products';
import { CheckCircle, Download, ShoppingCart, Users, Shield, Zap } from 'lucide-react';

export const metadata: Metadata = {
  title: '个人版 - 麒麟虚拟化',
  description: '麒麟虚拟化个人版，适合个人用户和小型团队使用，在麒麟OS上运行Windows应用程序。',
  keywords: '麒麟虚拟化,个人版,个人用户,小型团队,Windows应用'
};

const personalVersion = productVersions.find(v => v.id === 'personal')!;

export default function PersonalPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <section className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white">
        <div className="container mx-auto px-4 py-20">
          <div className="max-w-4xl mx-auto text-center">
            <Badge className="bg-white/20 text-white hover:bg-white/30 mb-4">
              个人版
            </Badge>
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              {personalVersion.name}
            </h1>
            <p className="text-xl text-blue-100 mb-8">
              {personalVersion.description}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-100">
                <ShoppingCart className="mr-2 h-4 w-4" />
                立即购买 ¥{personalVersion.price.monthly}/月
              </Button>
              <Button size="lg" variant="outline" className="border-white text-white hover:bg-white/10">
                <Download className="mr-2 h-4 w-4" />
                免费试用 30 天
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Price */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                简单透明的定价
              </h2>
              <p className="text-gray-600">
                适合个人用户和小型团队的经济实惠选择
              </p>
            </div>

            <div className="grid md:grid-cols-2 gap-8">
              <Card className="border-2 border-blue-500">
                <CardHeader className="text-center">
                  <CardTitle className="text-2xl">月付</CardTitle>
                  <CardDescription>按月付费，随时取消</CardDescription>
                  <div className="text-4xl font-bold text-blue-600 mt-4">
                    ¥{personalVersion.price.monthly}
                    <span className="text-lg font-normal text-gray-600">/月</span>
                  </div>
                </CardHeader>
                <CardContent>
                  <Button className="w-full bg-blue-600 hover:bg-blue-700">
                    选择月付方案
                  </Button>
                </CardContent>
              </Card>

              <Card className="border-2 border-green-500 relative">
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <Badge className="bg-green-600 text-white">推荐</Badge>
                </div>
                <CardHeader className="text-center">
                  <CardTitle className="text-2xl">年付</CardTitle>
                  <CardDescription>年付享受折扣优惠</CardDescription>
                  <div className="text-4xl font-bold text-green-600 mt-4">
                    ¥{personalVersion.price.yearly}
                    <span className="text-lg font-normal text-gray-600">/年</span>
                  </div>
                  <div className="text-sm text-green-600 font-medium">
                    省 ¥{personalVersion.price.monthly * 12 - personalVersion.price.yearly}
                  </div>
                </CardHeader>
                <CardContent>
                  <Button className="w-full bg-green-600 hover:bg-green-700">
                    选择年付方案
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Features */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                个人版功能特性
              </h2>
              <p className="text-xl text-gray-600">
                为个人用户和小型团队量身定制的功能
              </p>
            </div>

            <div className="grid md:grid-cols-3 gap-8 mb-16">
              <Card className="text-center">
                <CardHeader>
                  <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Users className="w-8 h-8 text-blue-600" />
                  </div>
                  <CardTitle>简单易用</CardTitle>
                  <CardDescription>
                    直观的界面设计，5分钟内完成安装配置
                  </CardDescription>
                </CardHeader>
              </Card>

              <Card className="text-center">
                <CardHeader>
                  <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Shield className="w-8 h-8 text-green-600" />
                  </div>
                  <CardTitle>安全可靠</CardTitle>
                  <CardDescription>
                    完全隔离的虚拟环境，保护主机系统安全
                  </CardDescription>
                </CardHeader>
              </Card>

              <Card className="text-center">
                <CardHeader>
                  <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Zap className="w-8 h-8 text-purple-600" />
                  </div>
                  <CardTitle>高性能</CardTitle>
                  <CardDescription>
                    硬件加速支持，Windows应用运行流畅
                  </CardDescription>
                </CardHeader>
              </Card>
            </div>

            <div className="bg-white rounded-lg shadow-lg p-8">
              <h3 className="text-2xl font-bold text-gray-900 mb-6">
                完整功能列表
              </h3>
              <div className="grid md:grid-cols-2 gap-6">
                {personalVersion.features.map((feature, index) => (
                  <div key={index} className="flex items-start space-x-3">
                    {feature.included ? (
                      <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5" />
                    ) : (
                      <div className="w-5 h-5 rounded-full bg-gray-200 flex-shrink-0 mt-0.5" />
                    )}
                    <div className={feature.included ? 'text-gray-900' : 'text-gray-400'}>
                      <div className="font-medium">{feature.name}</div>
                      <div className="text-sm">{feature.description}</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Use Cases */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                适用场景
              </h2>
              <p className="text-xl text-gray-600">
                个人版适合以下使用场景
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                {
                  title: '个人办公',
                  description: '在麒麟OS上使用Windows办公软件，如Office、WPS等',
                  icon: '💼'
                },
                {
                  title: '学习研究',
                  description: '学生和研究人员使用Windows专业软件进行学习',
                  icon: '📚'
                },
                {
                  title: '软件开发',
                  description: '开发者在麒麟OS上使用Windows开发工具',
                  icon: '💻'
                },
                {
                  title: '图形设计',
                  description: '使用Windows图形设计软件进行创意工作',
                  icon: '🎨'
                },
                {
                  title: '游戏娱乐',
                  description: '在麒麟OS上运行Windows游戏和娱乐软件',
                  icon: '🎮'
                },
                {
                  title: '测试验证',
                  description: '软件测试和系统验证工作',
                  icon: '🔬'
                }
              ].map((useCase, index) => (
                <Card key={index} className="text-center">
                  <CardHeader>
                    <div className="text-4xl mb-4">{useCase.icon}</div>
                    <CardTitle className="text-lg">{useCase.title}</CardTitle>
                    <CardDescription>{useCase.description}</CardDescription>
                  </CardHeader>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* CTA */}
      <section className="py-20 bg-blue-600 text-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl font-bold mb-4">
              立即开始使用麒麟虚拟化个人版
            </h2>
            <p className="text-xl text-blue-100 mb-8">
              30天免费试用，无需信用卡，体验完整功能
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-100">
                <Download className="mr-2 h-4 w-4" />
                免费试用 30 天
              </Button>
              <Button size="lg" variant="outline" className="border-white text-white hover:bg-white/10">
                <ShoppingCart className="mr-2 h-4 w-4" />
                立即购买
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}