import { Metadata } from 'next';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { productVersions } from '@/lib/data/products';
import { ShoppingCart, CheckCircle, Shield, Clock, Phone } from 'lucide-react';

export const metadata: Metadata = {
  title: '购买 - 麒麟虚拟化',
  description: '购买麒麟虚拟化软件，选择适合您的版本，支持多种支付方式，享受专业技术支持。',
  keywords: '麒麟虚拟化,购买,支付,订购,许可证'
};

export default function PurchasePage() {
  const selectedProduct = productVersions.find(p => p.id === 'professional') || productVersions[0];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <section className="bg-white border-b">
        <div className="container mx-auto px-4 py-16">
          <div className="max-w-4xl mx-auto text-center">
            <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-200 mb-4">
              购买产品
            </Badge>
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              购买麒麟虚拟化
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              选择适合您的版本，立即开始使用专业的虚拟化解决方案
            </p>
            <div className="flex items-center justify-center space-x-8 text-sm text-gray-600">
              <div className="flex items-center">
                <Shield className="w-4 h-4 mr-2 text-green-500" />
                安全支付
              </div>
              <div className="flex items-center">
                <CheckCircle className="w-4 h-4 mr-2 text-green-500" />
                即时激活
              </div>
              <div className="flex items-center">
                <Clock className="w-4 h-4 mr-2 text-green-500" />
                24/7技术支持
              </div>
            </div>
          </div>
        </div>
      </section>

      <div className="container mx-auto px-4 py-16">
        <div className="max-w-6xl mx-auto">
          <div className="grid lg:grid-cols-3 gap-8">
            {/* Purchase Form */}
            <div className="lg:col-span-2">
              <Card className="shadow-lg">
                <CardHeader>
                  <CardTitle className="text-2xl">购买信息</CardTitle>
                  <CardDescription>
                    请选择您需要的产品版本和数量
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Product Selection */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold">选择产品版本</h3>
                    <div className="grid gap-4">
                      {productVersions.map((version) => (
                        <div key={version.id} className="border rounded-lg p-4">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-3">
                              <input
                                type="radio"
                                id={version.id}
                                name="product"
                                className="w-4 h-4 text-blue-600"
                                defaultChecked={version.id === 'professional'}
                              />
                              <div>
                                <label htmlFor={version.id} className="font-medium text-gray-900 cursor-pointer">
                                  {version.name}
                                  {version.popular && (
                                    <Badge className="ml-2 bg-blue-600 text-white">推荐</Badge>
                                  )}
                                </label>
                                <p className="text-sm text-gray-600">{version.description}</p>
                              </div>
                            </div>
                            <div className="text-right">
                              <div className="font-bold text-lg">¥{version.price.monthly}/月</div>
                              <div className="text-sm text-gray-500">年付 ¥{version.price.yearly}</div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Billing Options */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold">计费周期</h3>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="border rounded-lg p-4">
                        <div className="flex items-center space-x-2">
                          <input
                            type="radio"
                            id="monthly"
                            name="billing"
                            className="w-4 h-4 text-blue-600"
                          />
                          <label htmlFor="monthly" className="font-medium cursor-pointer">
                            按月付费
                          </label>
                        </div>
                        <p className="text-sm text-gray-600 mt-1">¥{selectedProduct.price.monthly}/月</p>
                      </div>
                      <div className="border rounded-lg p-4 border-green-500 bg-green-50">
                        <div className="flex items-center space-x-2">
                          <input
                            type="radio"
                            id="yearly"
                            name="billing"
                            className="w-4 h-4 text-blue-600"
                            defaultChecked
                          />
                          <label htmlFor="yearly" className="font-medium cursor-pointer">
                            按年付费
                          </label>
                          <Badge className="bg-green-600 text-white text-xs">省钱</Badge>
                        </div>
                        <p className="text-sm text-gray-600 mt-1">¥{selectedProduct.price.yearly}/年</p>
                        <p className="text-xs text-green-600">省 ¥{selectedProduct.price.monthly * 12 - selectedProduct.price.yearly}</p>
                      </div>
                    </div>
                  </div>

                  {/* Contact Form */}
                  <div className="bg-blue-50 p-6 rounded-lg">
                    <h3 className="text-lg font-semibold mb-4">联系信息</h3>
                    <p className="text-sm text-gray-600 mb-4">
                      请联系我们的销售顾问完成购买流程，我们将为您提供个性化的产品配置和优惠价格。
                    </p>
                    <div className="flex flex-col sm:flex-row gap-4">
                      <Button className="bg-blue-600 hover:bg-blue-700">
                        <Phone className="mr-2 h-4 w-4" />
                        联系销售顾问
                      </Button>
                      <Button variant="outline">
                        预约产品演示
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Order Summary */}
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>订单摘要</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span>麒麟虚拟化专业版</span>
                    <span className="font-medium">¥{selectedProduct.price.yearly}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>许可证数量</span>
                    <span>1个</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>计费周期</span>
                    <span>按年付费</span>
                  </div>
                  <div className="border-t pt-4">
                    <div className="flex justify-between items-center font-bold text-lg">
                      <span>总计</span>
                      <span>¥{selectedProduct.price.yearly}</span>
                    </div>
                    <div className="text-sm text-green-600">
                      相比月付节省 ¥{selectedProduct.price.monthly * 12 - selectedProduct.price.yearly}
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>购买保障</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <Shield className="w-4 h-4 text-green-500" />
                    <span className="text-sm">30天退款保证</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="w-4 h-4 text-green-500" />
                    <span className="text-sm">即时激活和下载</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Clock className="w-4 h-4 text-green-500" />
                    <span className="text-sm">24/7技术支持</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Shield className="w-4 h-4 text-green-500" />
                    <span className="text-sm">安全加密支付</span>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>需要帮助？</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-600 mb-4">
                    我们的销售顾问随时为您提供帮助
                  </p>
                  <div className="space-y-2">
                    <Button variant="outline" className="w-full">
                      <Phone className="mr-2 h-4 w-4" />
                      联系销售顾问
                    </Button>
                    <div className="text-center text-sm text-gray-500">
                      销售热线: ************
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}