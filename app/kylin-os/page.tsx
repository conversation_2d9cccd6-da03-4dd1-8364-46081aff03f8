import { Metadata } from 'next';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Shield, 
  Zap, 
  Lock, 
  Cpu, 
  Globe, 
  Users, 
  CheckCircle, 
  Star,
  Monitor,
  HardDrive,
  Wifi,
  Database,
  Server,
  FileText,
  Download,
  Play
} from 'lucide-react';

export const metadata: Metadata = {
  title: '麒麟OS特色功能 - 麒麟虚拟化',
  description: '专为麒麟操作系统优化的虚拟化解决方案，提供深度系统集成、国产化安全认证、卓越性能优化。',
  keywords: '麒麟OS,虚拟化,国产化,安全认证,系统集成,性能优化'
};

export default function KylinOSPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-600 to-purple-700 text-white">
        <div className="container mx-auto px-4 py-20">
          <div className="max-w-4xl mx-auto text-center">
            <Badge className="bg-white/20 text-white hover:bg-white/30 mb-4">
              专为麒麟OS设计
            </Badge>
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              麒麟OS
              <span className="text-yellow-400"> 深度集成</span>
            </h1>
            <p className="text-xl mb-8 opacity-90">
              专业虚拟化解决方案，为麒麟操作系统量身定制
              <br />
              国产化认证 • 安全可控 • 性能卓越
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-100">
                <Download className="mr-2 h-5 w-5" />
                免费试用
              </Button>
              <Button size="lg" variant="outline" className="border-white text-white hover:bg-white/10">
                <Play className="mr-2 h-5 w-5" />
                观看演示
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Key Features */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              专为麒麟OS优化的核心特性
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              深度集成麒麟操作系统内核，提供无与伦比的虚拟化体验
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <Card className="text-center group hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4 group-hover:bg-red-200 transition-colors">
                  <Shield className="w-8 h-8 text-red-600" />
                </div>
                <CardTitle className="text-xl">国产化认证</CardTitle>
                <CardDescription>通过国家级安全认证</CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li className="flex items-center">
                    <CheckCircle className="w-4 h-4 mr-2 text-green-500" />
                    通过信创认证
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="w-4 h-4 mr-2 text-green-500" />
                    符合等保要求
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="w-4 h-4 mr-2 text-green-500" />
                    自主可控技术
                  </li>
                </ul>
              </CardContent>
            </Card>

            <Card className="text-center group hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4 group-hover:bg-blue-200 transition-colors">
                  <Zap className="w-8 h-8 text-blue-600" />
                </div>
                <CardTitle className="text-xl">极致性能</CardTitle>
                <CardDescription>针对麒麟OS深度优化</CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li className="flex items-center">
                    <CheckCircle className="w-4 h-4 mr-2 text-green-500" />
                    95%+ 原生性能
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="w-4 h-4 mr-2 text-green-500" />
                    硬件加速支持
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="w-4 h-4 mr-2 text-green-500" />
                    内存优化算法
                  </li>
                </ul>
              </CardContent>
            </Card>

            <Card className="text-center group hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4 group-hover:bg-green-200 transition-colors">
                  <Monitor className="w-8 h-8 text-green-600" />
                </div>
                <CardTitle className="text-xl">无缝集成</CardTitle>
                <CardDescription>完美融入麒麟OS生态</CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li className="flex items-center">
                    <CheckCircle className="w-4 h-4 mr-2 text-green-500" />
                    统一桌面体验
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="w-4 h-4 mr-2 text-green-500" />
                    文件无缝共享
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="w-4 h-4 mr-2 text-green-500" />
                    设备直通支持
                  </li>
                </ul>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Compatibility Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              麒麟OS版本兼容性
            </h2>
            <p className="text-xl text-gray-600">
              支持主流麒麟操作系统版本，确保最佳兼容性
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card className="text-center">
              <CardHeader className="pb-3">
                <div className="mx-auto w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-3">
                  <Server className="w-6 h-6 text-blue-600" />
                </div>
                <CardTitle className="text-lg">麒麟桌面版 V10</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 text-sm">
                  <div className="flex items-center justify-between">
                    <span>兼容性</span>
                    <Badge className="bg-green-100 text-green-800">完全支持</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>性能</span>
                    <div className="flex">
                      {[...Array(5)].map((_, i) => (
                        <Star key={i} className="w-3 h-3 text-yellow-400 fill-current" />
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardHeader className="pb-3">
                <div className="mx-auto w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-3">
                  <Monitor className="w-6 h-6 text-green-600" />
                </div>
                <CardTitle className="text-lg">麒麟桌面版 V4</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 text-sm">
                  <div className="flex items-center justify-between">
                    <span>兼容性</span>
                    <Badge className="bg-green-100 text-green-800">完全支持</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>性能</span>
                    <div className="flex">
                      {[...Array(5)].map((_, i) => (
                        <Star key={i} className="w-3 h-3 text-yellow-400 fill-current" />
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardHeader className="pb-3">
                <div className="mx-auto w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-3">
                  <Database className="w-6 h-6 text-purple-600" />
                </div>
                <CardTitle className="text-lg">麒麟服务器版 V10</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 text-sm">
                  <div className="flex items-center justify-between">
                    <span>兼容性</span>
                    <Badge className="bg-green-100 text-green-800">完全支持</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>性能</span>
                    <div className="flex">
                      {[...Array(5)].map((_, i) => (
                        <Star key={i} className="w-3 h-3 text-yellow-400 fill-current" />
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardHeader className="pb-3">
                <div className="mx-auto w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mb-3">
                  <Cpu className="w-6 h-6 text-orange-600" />
                </div>
                <CardTitle className="text-lg">麒麟嵌入式版</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 text-sm">
                  <div className="flex items-center justify-between">
                    <span>兼容性</span>
                    <Badge className="bg-yellow-100 text-yellow-800">部分支持</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>性能</span>
                    <div className="flex">
                      {[...Array(4)].map((_, i) => (
                        <Star key={i} className="w-3 h-3 text-yellow-400 fill-current" />
                      ))}
                      <Star className="w-3 h-3 text-gray-300" />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Security Features */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              企业级安全保障
            </h2>
            <p className="text-xl text-gray-600">
              符合国家信息安全标准，为政企用户提供可靠保障
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8">
            <Card className="p-8">
              <div className="flex items-start space-x-4">
                <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center flex-shrink-0">
                  <Lock className="w-6 h-6 text-red-600" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold mb-2">数据加密保护</h3>
                  <p className="text-gray-600 mb-4">
                    采用国产加密算法，确保虚拟机数据安全，支持透明加密和密钥管理。
                  </p>
                  <ul className="space-y-2 text-sm">
                    <li className="flex items-center">
                      <CheckCircle className="w-4 h-4 mr-2 text-green-500" />
                      SM2/SM3/SM4 国产加密
                    </li>
                    <li className="flex items-center">
                      <CheckCircle className="w-4 h-4 mr-2 text-green-500" />
                      密钥安全存储
                    </li>
                    <li className="flex items-center">
                      <CheckCircle className="w-4 h-4 mr-2 text-green-500" />
                      传输层加密
                    </li>
                  </ul>
                </div>
              </div>
            </Card>

            <Card className="p-8">
              <div className="flex items-start space-x-4">
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                  <Shield className="w-6 h-6 text-blue-600" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold mb-2">访问控制</h3>
                  <p className="text-gray-600 mb-4">
                    完整的身份认证和权限管理体系，支持多种认证方式和细粒度权限控制。
                  </p>
                  <ul className="space-y-2 text-sm">
                    <li className="flex items-center">
                      <CheckCircle className="w-4 h-4 mr-2 text-green-500" />
                      多因素认证
                    </li>
                    <li className="flex items-center">
                      <CheckCircle className="w-4 h-4 mr-2 text-green-500" />
                      角色权限管理
                    </li>
                    <li className="flex items-center">
                      <CheckCircle className="w-4 h-4 mr-2 text-green-500" />
                      审计日志记录
                    </li>
                  </ul>
                </div>
              </div>
            </Card>

            <Card className="p-8">
              <div className="flex items-start space-x-4">
                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center flex-shrink-0">
                  <Users className="w-6 h-6 text-green-600" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold mb-2">隔离防护</h3>
                  <p className="text-gray-600 mb-4">
                    强化虚拟机隔离机制，防止虚拟机间的恶意攻击和数据泄露。
                  </p>
                  <ul className="space-y-2 text-sm">
                    <li className="flex items-center">
                      <CheckCircle className="w-4 h-4 mr-2 text-green-500" />
                      虚拟机完全隔离
                    </li>
                    <li className="flex items-center">
                      <CheckCircle className="w-4 h-4 mr-2 text-green-500" />
                      网络安全隔离
                    </li>
                    <li className="flex items-center">
                      <CheckCircle className="w-4 h-4 mr-2 text-green-500" />
                      资源访问控制
                    </li>
                  </ul>
                </div>
              </div>
            </Card>

            <Card className="p-8">
              <div className="flex items-start space-x-4">
                <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center flex-shrink-0">
                  <FileText className="w-6 h-6 text-purple-600" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold mb-2">合规认证</h3>
                  <p className="text-gray-600 mb-4">
                    通过多项国家级认证，满足政府和企业的合规要求。
                  </p>
                  <ul className="space-y-2 text-sm">
                    <li className="flex items-center">
                      <CheckCircle className="w-4 h-4 mr-2 text-green-500" />
                      等保三级认证
                    </li>
                    <li className="flex items-center">
                      <CheckCircle className="w-4 h-4 mr-2 text-green-500" />
                      信创产品认证
                    </li>
                    <li className="flex items-center">
                      <CheckCircle className="w-4 h-4 mr-2 text-green-500" />
                      CC EAL4+ 认证
                    </li>
                  </ul>
                </div>
              </div>
            </Card>
          </div>
        </div>
      </section>

      {/* Performance Optimization */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              性能优化技术
            </h2>
            <p className="text-xl text-gray-600">
              专门针对麒麟OS优化，提供接近原生的性能表现
            </p>
          </div>

          <div className="grid lg:grid-cols-3 gap-8">
            <div className="lg:col-span-2">
              <div className="grid md:grid-cols-2 gap-6">
                <Card className="p-6">
                  <div className="flex items-center mb-4">
                    <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                      <Cpu className="w-5 h-5 text-blue-600" />
                    </div>
                    <h3 className="text-lg font-semibold">硬件加速</h3>
                  </div>
                  <p className="text-gray-600 text-sm mb-3">
                    充分利用麒麟OS的硬件加速特性，提升虚拟化性能。
                  </p>
                  <ul className="space-y-1 text-sm">
                    <li className="flex items-center">
                      <CheckCircle className="w-3 h-3 mr-2 text-green-500" />
                      GPU 直通支持
                    </li>
                    <li className="flex items-center">
                      <CheckCircle className="w-3 h-3 mr-2 text-green-500" />
                      VT-x/AMD-V 优化
                    </li>
                  </ul>
                </Card>

                <Card className="p-6">
                  <div className="flex items-center mb-4">
                    <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                      <HardDrive className="w-5 h-5 text-green-600" />
                    </div>
                    <h3 className="text-lg font-semibold">内存优化</h3>
                  </div>
                  <p className="text-gray-600 text-sm mb-3">
                    智能内存管理，减少内存占用，提高运行效率。
                  </p>
                  <ul className="space-y-1 text-sm">
                    <li className="flex items-center">
                      <CheckCircle className="w-3 h-3 mr-2 text-green-500" />
                      内存气球技术
                    </li>
                    <li className="flex items-center">
                      <CheckCircle className="w-3 h-3 mr-2 text-green-500" />
                      内存去重算法
                    </li>
                  </ul>
                </Card>

                <Card className="p-6">
                  <div className="flex items-center mb-4">
                    <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                      <Wifi className="w-5 h-5 text-purple-600" />
                    </div>
                    <h3 className="text-lg font-semibold">网络优化</h3>
                  </div>
                  <p className="text-gray-600 text-sm mb-3">
                    优化网络栈，减少网络延迟，提升网络性能。
                  </p>
                  <ul className="space-y-1 text-sm">
                    <li className="flex items-center">
                      <CheckCircle className="w-3 h-3 mr-2 text-green-500" />
                      SR-IOV 支持
                    </li>
                    <li className="flex items-center">
                      <CheckCircle className="w-3 h-3 mr-2 text-green-500" />
                      网络队列优化
                    </li>
                  </ul>
                </Card>

                <Card className="p-6">
                  <div className="flex items-center mb-4">
                    <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center mr-3">
                      <Zap className="w-5 h-5 text-orange-600" />
                    </div>
                    <h3 className="text-lg font-semibold">启动优化</h3>
                  </div>
                  <p className="text-gray-600 text-sm mb-3">
                    快速启动技术，大幅缩短虚拟机启动时间。
                  </p>
                  <ul className="space-y-1 text-sm">
                    <li className="flex items-center">
                      <CheckCircle className="w-3 h-3 mr-2 text-green-500" />
                      快照恢复
                    </li>
                    <li className="flex items-center">
                      <CheckCircle className="w-3 h-3 mr-2 text-green-500" />
                      预加载缓存
                    </li>
                  </ul>
                </Card>
              </div>
            </div>

            <div>
              <Card className="p-6">
                <h3 className="text-lg font-semibold mb-4">性能基准测试</h3>
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between mb-2">
                      <span className="text-sm font-medium">CPU性能</span>
                      <span className="text-sm text-gray-600">95%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div className="bg-blue-600 h-2 rounded-full" style={{ width: '95%' }}></div>
                    </div>
                  </div>
                  <div>
                    <div className="flex justify-between mb-2">
                      <span className="text-sm font-medium">内存性能</span>
                      <span className="text-sm text-gray-600">92%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div className="bg-green-600 h-2 rounded-full" style={{ width: '92%' }}></div>
                    </div>
                  </div>
                  <div>
                    <div className="flex justify-between mb-2">
                      <span className="text-sm font-medium">网络性能</span>
                      <span className="text-sm text-gray-600">88%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div className="bg-purple-600 h-2 rounded-full" style={{ width: '88%' }}></div>
                    </div>
                  </div>
                  <div>
                    <div className="flex justify-between mb-2">
                      <span className="text-sm font-medium">磁盘I/O</span>
                      <span className="text-sm text-gray-600">85%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div className="bg-orange-600 h-2 rounded-full" style={{ width: '85%' }}></div>
                    </div>
                  </div>
                </div>
                <p className="text-xs text-gray-500 mt-4">
                  * 基于标准测试环境的性能数据
                </p>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-20 bg-gradient-to-r from-blue-600 to-purple-700 text-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              体验专为麒麟OS打造的虚拟化解决方案
            </h2>
            <p className="text-xl mb-8 opacity-90">
              立即下载免费试用版，感受国产化虚拟化技术的强大功能
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-100">
                <Download className="mr-2 h-5 w-5" />
                立即下载试用
              </Button>
              <Button size="lg" variant="outline" className="border-white text-white hover:bg-white/10">
                联系销售顾问
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}