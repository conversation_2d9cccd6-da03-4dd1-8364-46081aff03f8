import type { Metadata, Viewport } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import Navigation from '@/components/layout/navigation'
import Footer from '@/components/layout/footer'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: {
    default: 'Quintar - 专业的麒麟OS虚拟化解决方案',
    template: '%s | Quintar'
  },
  description: '专为麒麟操作系统打造的专业虚拟化软件，让您在国产操作系统上无缝运行Windows应用程序。支持个人版、专业版、企业版，提供完整的虚拟化解决方案。',
  keywords: ['麒麟操作系统', '虚拟化', 'Windows应用', '国产化', '企业级安全', '信创', '麒麟系统', '软件虚拟化'],
  authors: [{ name: 'Quintar科技有限公司' }],
  creator: 'Quintar科技有限公司',
  publisher: 'Quintar科技有限公司',
  metadataBase: new URL(process.env.NEXT_PUBLIC_SITE_URL || 'https://kylin-virt.com'),
  appleWebApp: {
    capable: true,
    statusBarStyle: 'default',
    title: 'Quintar'
  },
  openGraph: {
    type: 'website',
    locale: 'zh_CN',
    url: process.env.NEXT_PUBLIC_SITE_URL || 'https://kylin-virt.com',
    siteName: 'Quintar',
    title: 'Quintar - 专业的麒麟OS虚拟化解决方案',
    description: '专为麒麟操作系统打造的专业虚拟化软件，让您在国产操作系统上无缝运行Windows应用程序。',
    images: [
      {
        url: '/placeholder.jpg',
        width: 1200,
        height: 630,
        alt: 'Quintar - 专业虚拟化解决方案'
      }
    ]
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Quintar - 专业的麒麟OS虚拟化解决方案',
    description: '专为麒麟操作系统打造的专业虚拟化软件，让您在国产操作系统上无缝运行Windows应用程序。',
    images: ['/placeholder.jpg']
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: process.env.GOOGLE_VERIFICATION,
    yandex: process.env.YANDEX_VERIFICATION,
    other: {
      baidu: [process.env.BAIDU_VERIFICATION || ''],
    },
  },
}

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 5,
  userScalable: true,
  themeColor: '#3b82f6',
  colorScheme: 'light'
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "SoftwareApplication",
    "name": "Quintar",
    "description": "专为麒麟操作系统打造的专业虚拟化软件",
    "operatingSystem": "麒麟操作系统",
    "applicationCategory": "VirtualizationSoftware",
    "offers": {
      "@type": "Offer",
      "price": "99",
      "priceCurrency": "CNY",
      "availability": "https://schema.org/InStock"
    },
    "publisher": {
      "@type": "Organization",
      "name": "Quintar科技有限公司",
      "url": process.env.NEXT_PUBLIC_SITE_URL || "https://kylin-virt.com"
    },
    "sameAs": [
      `${process.env.NEXT_PUBLIC_SITE_URL || "https://kylin-virt.com"}/products`,
      `${process.env.NEXT_PUBLIC_SITE_URL || "https://kylin-virt.com"}/kylin-os`
    ]
  };

  return (
    <html lang="zh-CN" className="smooth-scroll">
      <head>
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
        />
      </head>
      <body className={`${inter.className} prevent-horizontal-scroll mobile-optimized-text antialiased`}>
        <Navigation />
        <main className="min-h-screen">
          {children}
        </main>
        <Footer />
      </body>
    </html>
  )
}
