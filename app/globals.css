@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.75rem;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Mobile optimizations */
@layer utilities {
  /* Improved touch targets for mobile */
  .touch-target {
    @apply min-h-[44px] min-w-[44px];
  }
  
  /* Better scroll behavior on mobile */
  .smooth-scroll {
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
  }
  
  /* Prevent horizontal scroll on mobile */
  .prevent-horizontal-scroll {
    overflow-x: hidden;
  }
  
  /* Better text rendering on mobile */
  .mobile-optimized-text {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
  }
  
  /* Safe area insets for mobile devices */
  .safe-area-top {
    padding-top: env(safe-area-inset-top);
  }
  
  .safe-area-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }
  
  /* Mobile-specific animations */
  .mobile-fade-in {
    animation: mobile-fade-in 0.3s ease-out;
  }
  
  @keyframes mobile-fade-in {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  /* Better button styles for mobile */
  .mobile-button {
    @apply min-h-[48px] px-4 py-3 text-base;
  }
  
  /* Card hover effects disabled on mobile */
  @media (hover: hover) {
    .card-hover:hover {
      @apply shadow-lg scale-105;
      transition: all 0.2s ease-in-out;
    }
  }
  
  /* Mobile-specific spacing */
  .mobile-container {
    @apply px-4 sm:px-6 lg:px-8;
  }
  
  /* Responsive text sizing */
  .responsive-text-xl {
    @apply text-lg sm:text-xl lg:text-2xl;
  }
  
  .responsive-text-2xl {
    @apply text-xl sm:text-2xl lg:text-3xl;
  }
  
  .responsive-text-3xl {
    @apply text-2xl sm:text-3xl lg:text-4xl;
  }
  
  .responsive-text-4xl {
    @apply text-3xl sm:text-4xl lg:text-5xl;
  }
}

/* Chinese font optimizations */
@layer base {
  /* Chinese font stack */
  .font-chinese {
    font-family: 'PingFang SC', 'Hiragino Sans GB', 'Noto Sans CJK SC', 'Source Han Sans SC', 'Microsoft YaHei', '微软雅黑', 'SimSun', '宋体', sans-serif;
  }
  
  /* Latin font stack */
  .font-latin {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  }
  
  /* Optimized text rendering for Chinese */
  .chinese-text-optimize {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
    font-variant-ligatures: none;
    word-break: break-word;
    overflow-wrap: break-word;
  }
  
  /* Chinese punctuation optimization */
  .chinese-punctuation {
    /* Proper spacing for Chinese punctuation */
    text-spacing: trim-start allow-end ideograph-alpha ideograph-numeric;
  }
  
  /* Better line height for Chinese text */
  .chinese-line-height {
    line-height: 1.7;
  }
  
  /* Mixed Chinese-English text optimization */
  .mixed-text {
    font-variant-numeric: proportional-nums;
    font-feature-settings: 'kern' 1, 'liga' 1;
  }
}

/* Performance optimizations */
@layer base {
  /* GPU acceleration for animations */
  .gpu-accelerated {
    transform: translateZ(0);
    will-change: transform;
  }
  
  /* Reduce motion for users who prefer it */
  @media (prefers-reduced-motion: reduce) {
    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }
}