import { Metadata } from 'next';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { 
  BookOpen, 
  Search, 
  Download, 
  Play, 
  FileText, 
  Settings, 
  Zap, 
  Shield, 
  Code, 
  Globe,
  ArrowRight,
  Clock,
  Star
} from 'lucide-react';
import Link from 'next/link';

export const metadata: Metadata = {
  title: '文档中心 - 麒麟虚拟化',
  description: '完整的技术文档和使用指南，帮助您快速上手和高效使用麒麟虚拟化软件。',
  keywords: '文档,技术文档,使用指南,API文档,麒麟虚拟化'
};

const documentCategories = [
  {
    icon: BookOpen,
    title: '快速入门',
    description: '从零开始学习使用麒麟虚拟化',
    color: 'blue',
    docs: [
      { title: '安装指南', href: '/support/docs/installation', time: '10分钟' },
      { title: '快速入门教程', href: '/support/docs/quickstart', time: '15分钟' },
      { title: '界面介绍', href: '/support/docs/interface', time: '8分钟' },
      { title: '创建第一个虚拟机', href: '/support/docs/first-vm', time: '20分钟' }
    ]
  },
  {
    icon: Settings,
    title: '配置管理',
    description: '虚拟机配置和系统设置',
    color: 'green',
    docs: [
      { title: '虚拟机设置', href: '/support/docs/vm-settings', time: '12分钟' },
      { title: '网络配置', href: '/support/docs/network', time: '15分钟' },
      { title: '存储管理', href: '/support/docs/storage', time: '10分钟' },
      { title: '资源分配', href: '/support/docs/resources', time: '8分钟' }
    ]
  },
  {
    icon: Zap,
    title: '性能优化',
    description: '提升虚拟机性能的最佳实践',
    color: 'yellow',
    docs: [
      { title: '性能调优指南', href: '/support/docs/performance', time: '18分钟' },
      { title: '内存优化', href: '/support/docs/memory', time: '12分钟' },
      { title: '硬件加速', href: '/support/docs/acceleration', time: '15分钟' },
      { title: '监控和诊断', href: '/support/docs/monitoring', time: '10分钟' }
    ]
  },
  {
    icon: Shield,
    title: '安全防护',
    description: '虚拟机安全和数据保护',
    color: 'red',
    docs: [
      { title: '安全最佳实践', href: '/support/docs/security', time: '16分钟' },
      { title: '数据加密', href: '/support/docs/encryption', time: '14分钟' },
      { title: '访问控制', href: '/support/docs/access-control', time: '12分钟' },
      { title: '备份策略', href: '/support/docs/backup', time: '10分钟' }
    ]
  },
  {
    icon: Code,
    title: '开发者文档',
    description: 'API文档和开发者资源',
    color: 'purple',
    docs: [
      { title: 'API 参考', href: '/support/docs/api', time: '30分钟' },
      { title: 'SDK 使用指南', href: '/support/docs/sdk', time: '25分钟' },
      { title: '插件开发', href: '/support/docs/plugins', time: '20分钟' },
      { title: '脚本自动化', href: '/support/docs/automation', time: '15分钟' }
    ]
  },
  {
    icon: Globe,
    title: '集成指南',
    description: '与其他系统和工具的集成',
    color: 'indigo',
    docs: [
      { title: '企业AD集成', href: '/support/docs/ad-integration', time: '20分钟' },
      { title: '监控系统集成', href: '/support/docs/monitoring-integration', time: '18分钟' },
      { title: '备份工具集成', href: '/support/docs/backup-integration', time: '15分钟' },
      { title: '自动化部署', href: '/support/docs/deployment', time: '25分钟' }
    ]
  }
];

const featuredDocs = [
  {
    title: '麒麟OS虚拟化完整指南',
    description: '从安装到高级配置的完整使用指南',
    category: '完整指南',
    readTime: '45分钟',
    rating: 5,
    href: '/support/docs/complete-guide'
  },
  {
    title: '故障排除手册',
    description: '常见问题的诊断和解决方案',
    category: '故障排除',
    readTime: '30分钟',
    rating: 5,
    href: '/support/docs/troubleshooting'
  },
  {
    title: '企业部署最佳实践',
    description: '企业环境中的部署和管理指南',
    category: '企业解决方案',
    readTime: '35分钟',
    rating: 4,
    href: '/support/docs/enterprise-deployment'
  }
];

const recentUpdates = [
  {
    title: 'V2.1 新功能介绍',
    description: '了解最新版本的新特性',
    date: '2024-01-15',
    href: '/support/docs/v2-1-features'
  },
  {
    title: '安全更新指南',
    description: '重要安全更新的安装说明',
    date: '2024-01-10',
    href: '/support/docs/security-updates'
  },
  {
    title: '性能优化技巧更新',
    description: '新增的性能调优方法',
    date: '2024-01-05',
    href: '/support/docs/performance-tips'
  }
];

export default function DocsPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <section className="bg-white border-b">
        <div className="container mx-auto px-4 py-16">
          <div className="max-w-4xl mx-auto text-center">
            <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-200 mb-4">
              文档中心
            </Badge>
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              完整的技术文档
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              从入门到精通，全面的使用指南和技术文档
            </p>
            
            {/* Search Bar */}
            <div className="max-w-2xl mx-auto mb-8">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <Input 
                  type="text" 
                  placeholder="搜索文档..." 
                  className="pl-10 pr-4 py-3 text-lg"
                />
                <Button className="absolute right-2 top-1/2 transform -translate-y-1/2">
                  搜索
                </Button>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="flex flex-wrap justify-center gap-4">
              <Button variant="outline">
                <Download className="mr-2 w-4 h-4" />
                下载离线文档
              </Button>
              <Button variant="outline">
                <Play className="mr-2 w-4 h-4" />
                视频教程
              </Button>
              <Button variant="outline">
                <FileText className="mr-2 w-4 h-4" />
                PDF版本
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Documentation */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">精选文档</h2>
            <p className="text-gray-600">最受欢迎和最重要的文档指南</p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {featuredDocs.map((doc) => (
              <Card key={doc.href} className="group hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex items-center justify-between mb-2">
                    <Badge variant="outline" className="text-xs">
                      {doc.category}
                    </Badge>
                    <div className="flex items-center">
                      {[...Array(5)].map((_, i) => (
                        <Star 
                          key={i} 
                          className={`w-3 h-3 ${i < doc.rating ? 'text-yellow-400 fill-current' : 'text-gray-300'}`} 
                        />
                      ))}
                    </div>
                  </div>
                  <CardTitle className="text-lg group-hover:text-blue-600 transition-colors">
                    <Link href={doc.href}>{doc.title}</Link>
                  </CardTitle>
                  <CardDescription>{doc.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500 flex items-center">
                      <Clock className="w-3 h-3 mr-1" />
                      {doc.readTime}
                    </span>
                    <Button variant="ghost" size="sm" asChild>
                      <Link href={doc.href}>
                        阅读
                        <ArrowRight className="ml-1 w-4 h-4" />
                      </Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Documentation Categories */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">文档分类</h2>
            <p className="text-gray-600">按主题浏览完整的文档库</p>
          </div>

          <div className="grid lg:grid-cols-2 gap-8">
            {documentCategories.map((category) => (
              <Card key={category.title} className="group hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex items-center space-x-3 mb-4">
                    <div className={`w-12 h-12 bg-${category.color}-100 rounded-lg flex items-center justify-center`}>
                      <category.icon className={`w-6 h-6 text-${category.color}-600`} />
                    </div>
                    <div>
                      <CardTitle className="text-xl">{category.title}</CardTitle>
                      <CardDescription>{category.description}</CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {category.docs.map((doc) => (
                      <div key={doc.href} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                        <Link href={doc.href} className="flex-1">
                          <div className="flex items-center justify-between">
                            <span className="font-medium text-gray-900 hover:text-blue-600 transition-colors">
                              {doc.title}
                            </span>
                            <span className="text-sm text-gray-500 flex items-center">
                              <Clock className="w-3 h-3 mr-1" />
                              {doc.time}
                            </span>
                          </div>
                        </Link>
                        <ArrowRight className="w-4 h-4 text-gray-400 ml-3" />
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Recent Updates */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">最近更新</h2>
              <p className="text-gray-600">查看最新的文档更新和新增内容</p>
            </div>

            <div className="space-y-6">
              {recentUpdates.map((update) => (
                <Card key={update.href} className="group hover:shadow-md transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
                          <Link href={update.href}>{update.title}</Link>
                        </h3>
                        <p className="text-gray-600 mt-1">{update.description}</p>
                        <p className="text-sm text-gray-500 mt-2">{update.date}</p>
                      </div>
                      <Button variant="outline" size="sm" asChild>
                        <Link href={update.href}>
                          查看
                          <ArrowRight className="ml-1 w-4 h-4" />
                        </Link>
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-16 bg-gradient-to-r from-blue-600 to-purple-700 text-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              需要更多帮助？
            </h2>
            <p className="text-xl mb-8 opacity-90">
              我们的技术支持团队随时准备为您提供个性化的帮助和指导
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-100">
                联系技术支持
              </Button>
              <Button size="lg" variant="outline" className="border-white text-white hover:bg-white/10">
                预约培训
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}