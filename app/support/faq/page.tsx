import { Metadata } from 'next';
import { 
  Accordion, 
  AccordionContent, 
  AccordionItem, 
  AccordionTrigger 
} from '@/components/ui/accordion';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Search, MessageCircle, Phone, Mail } from 'lucide-react';

export const metadata: Metadata = {
  title: '常见问题 - 麒麟虚拟化',
  description: '查找麒麟虚拟化软件的常见问题解答，包括安装、配置、故障排除等。',
  keywords: 'FAQ,常见问题,帮助,麒麟虚拟化,故障排除'
};

const faqCategories = [
  {
    id: 'installation',
    title: '安装和配置',
    questions: [
      {
        q: '麒麟虚拟化软件支持哪些操作系统版本？',
        a: '我们支持麒麟桌面版 V4.0+、麒麟桌面版 V10、麒麟服务器版 V10 等主流版本。具体兼容性信息请查看系统要求页面。'
      },
      {
        q: '如何在麒麟OS上安装虚拟化软件？',
        a: '请按照以下步骤：1. 从官网下载适合您系统的安装包；2. 使用 sudo dpkg -i 命令安装；3. 运行 kylin-virt-setup 进行初始化配置；4. 重启系统完成安装。详细步骤请参考安装指南。'
      },
      {
        q: '安装时出现依赖包错误怎么办？',
        a: '请先运行 sudo apt update && sudo apt upgrade 更新系统，然后使用 sudo apt install -f 修复依赖关系。如果问题仍然存在，请联系技术支持。'
      },
      {
        q: '是否需要特殊的硬件要求？',
        a: '需要支持硬件虚拟化的CPU（Intel VT-x 或 AMD-V），至少4GB内存（推荐8GB+），以及至少20GB的可用磁盘空间。'
      }
    ]
  },
  {
    id: 'licensing',
    title: '许可证和激活',
    questions: [
      {
        q: '如何激活我的许可证？',
        a: '启动软件后，点击"激活许可证"按钮，输入您收到的许可证密钥。确保您的系统已连接到互联网。激活成功后，您将看到确认消息。'
      },
      {
        q: '试用期结束后如何购买正式版？',
        a: '在试用期结束前，您会收到升级提醒。点击"立即购买"按钮选择适合的版本，完成支付后即可获得正式许可证。'
      },
      {
        q: '一个许可证可以在多台机器上使用吗？',
        a: '这取决于您购买的许可证类型。个人版允许在一台机器上使用，专业版支持3台设备，企业版支持无限制设备数量。'
      },
      {
        q: '如何迁移许可证到新设备？',
        a: '在原设备上使用"取消激活"功能，然后在新设备上重新激活许可证。如果遇到问题，请联系我们的支持团队。'
      }
    ]
  },
  {
    id: 'performance',
    title: '性能和优化',
    questions: [
      {
        q: '如何提高虚拟机性能？',
        a: '建议：1. 为虚拟机分配足够的内存和CPU资源；2. 启用硬件加速功能；3. 使用SSD存储；4. 定期清理虚拟机磁盘；5. 关闭不必要的视觉效果。'
      },
      {
        q: '虚拟机运行缓慢怎么办？',
        a: '检查以下几点：1. 主机系统资源是否充足；2. 虚拟机内存分配是否合理；3. 是否启用了硬件加速；4. 磁盘空间是否充足；5. 是否有其他程序占用大量资源。'
      },
      {
        q: '可以同时运行多少个虚拟机？',
        a: '这取决于您的硬件配置。一般来说，每个虚拟机至少需要1GB内存。在8GB内存的系统上，可以同时运行2-3个轻量级虚拟机。'
      },
      {
        q: '如何优化网络性能？',
        a: '建议使用桥接模式而非NAT模式，启用网络加速功能，并确保网络驱动程序是最新版本。'
      }
    ]
  },
  {
    id: 'troubleshooting',
    title: '故障排除',
    questions: [
      {
        q: '虚拟机无法启动怎么办？',
        a: '请检查：1. 主机系统是否支持虚拟化；2. 虚拟机配置是否正确；3. 磁盘空间是否充足；4. 内存分配是否合理。如果问题持续，请查看错误日志。'
      },
      {
        q: '如何解决蓝屏或崩溃问题？',
        a: '常见原因包括：1. 内存分配过多；2. 硬件兼容性问题；3. 驱动程序冲突。建议减少内存分配，更新驱动程序，或重新安装虚拟机。'
      },
      {
        q: '网络连接问题如何解决？',
        a: '检查网络设置：1. 确认网络模式配置正确；2. 检查防火墙设置；3. 重置网络适配器；4. 确保主机网络正常。'
      },
      {
        q: '如何备份和恢复虚拟机？',
        a: '使用"快照"功能创建虚拟机状态备份，也可以直接复制虚拟机文件。恢复时，使用"恢复快照"功能或替换虚拟机文件。'
      }
    ]
  },
  {
    id: 'features',
    title: '功能使用',
    questions: [
      {
        q: '如何在虚拟机和主机之间共享文件？',
        a: '安装客户端工具后，可以通过以下方式共享文件：1. 启用文件夹共享功能；2. 使用剪贴板共享；3. 拖拽文件到虚拟机窗口。'
      },
      {
        q: '如何设置虚拟机自动启动？',
        a: '在虚拟机设置中启用"随系统启动"选项，或者在麒麟OS的启动应用程序中添加虚拟机启动命令。'
      },
      {
        q: '支持哪些虚拟机格式？',
        a: '支持主流的虚拟机格式，包括VHD、VMDK、VDI、QCOW2等。可以直接导入其他虚拟化软件创建的虚拟机。'
      },
      {
        q: '如何调整虚拟机分辨率？',
        a: '安装客户端工具后，虚拟机分辨率会自动适应窗口大小。您也可以在虚拟机系统设置中手动调整分辨率。'
      }
    ]
  }
];

export default function FAQPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <section className="bg-white border-b">
        <div className="container mx-auto px-4 py-16">
          <div className="max-w-4xl mx-auto text-center">
            <Badge className="bg-green-100 text-green-800 hover:bg-green-200 mb-4">
              常见问题
            </Badge>
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              常见问题解答
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              快速找到您需要的答案，解决使用中遇到的问题
            </p>
            
            {/* Search Bar */}
            <div className="max-w-2xl mx-auto">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <Input 
                  type="text" 
                  placeholder="搜索问题..." 
                  className="pl-10 pr-4 py-3 text-lg"
                />
                <Button className="absolute right-2 top-1/2 transform -translate-y-1/2">
                  搜索
                </Button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Content */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            {faqCategories.map((category) => (
              <div key={category.id} className="mb-12">
                <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                  <span className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                    <span className="text-blue-600 font-bold text-sm">
                      {category.id === 'installation' ? '📦' : 
                       category.id === 'licensing' ? '🔑' : 
                       category.id === 'performance' ? '⚡' : 
                       category.id === 'troubleshooting' ? '🔧' : '⚙️'}
                    </span>
                  </span>
                  {category.title}
                </h2>
                
                <Card>
                  <CardContent className="p-0">
                    <Accordion type="single" collapsible>
                      {category.questions.map((faq, index) => (
                        <AccordionItem key={index} value={`${category.id}-${index}`}>
                          <AccordionTrigger className="px-6 py-4 text-left hover:bg-gray-50">
                            {faq.q}
                          </AccordionTrigger>
                          <AccordionContent className="px-6 pb-4 text-gray-600">
                            {faq.a}
                          </AccordionContent>
                        </AccordionItem>
                      ))}
                    </Accordion>
                  </CardContent>
                </Card>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Still Need Help */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl font-bold text-gray-900 mb-6">
              还没找到您需要的答案？
            </h2>
            <p className="text-xl text-gray-600 mb-8">
              我们的技术支持团队随时准备为您提供帮助
            </p>
            
            <div className="grid md:grid-cols-3 gap-6">
              <Card className="text-center">
                <CardHeader>
                  <div className="mx-auto w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                    <MessageCircle className="w-6 h-6 text-blue-600" />
                  </div>
                  <CardTitle className="text-lg">在线客服</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 mb-4">工作日实时在线支持</p>
                  <Button className="w-full">开始对话</Button>
                </CardContent>
              </Card>

              <Card className="text-center">
                <CardHeader>
                  <div className="mx-auto w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-4">
                    <Phone className="w-6 h-6 text-green-600" />
                  </div>
                  <CardTitle className="text-lg">电话支持</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 mb-4">************</p>
                  <Button variant="outline" className="w-full">立即拨打</Button>
                </CardContent>
              </Card>

              <Card className="text-center">
                <CardHeader>
                  <div className="mx-auto w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mb-4">
                    <Mail className="w-6 h-6 text-purple-600" />
                  </div>
                  <CardTitle className="text-lg">邮件支持</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 mb-4"><EMAIL></p>
                  <Button variant="outline" className="w-full">发送邮件</Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}