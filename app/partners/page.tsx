import { Metadata } from 'next';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Handshake, 
  Users, 
  Star,
  TrendingUp,
  Award,
  Globe,
  Building,
  Zap,
  Shield,
  ArrowRight,
  Mail,
  Phone,
  FileText,
  CheckCircle
} from 'lucide-react';

export const metadata: Metadata = {
  title: '合作伙伴计划 - 麒麟虚拟化',
  description: '加入我们的合作伙伴计划，共同推动国产虚拟化技术发展，实现互利共赢。',
  keywords: '合作伙伴,渠道合作,技术联盟,分销商,麒麟虚拟化'
};

const partnerTypes = [
  {
    icon: Building,
    title: '授权分销商',
    description: '成为我们的授权分销商，享受丰厚的利润空间',
    benefits: [
      '高达40%的销售佣金',
      '专享价格政策',
      '销售支持和培训',
      '市场推广支持'
    ],
    requirements: [
      '具备相关行业销售经验',
      '拥有稳定的客户资源',
      '年销售目标20万以上',
      '提供本地化技术支持'
    ],
    color: 'blue'
  },
  {
    icon: Users,
    title: '系统集成商',
    description: '与我们合作，为客户提供完整的解决方案',
    benefits: [
      '技术培训和认证',
      '解决方案支持',
      '联合销售机会',
      '技术预售支持'
    ],
    requirements: [
      '具备系统集成资质',
      '拥有技术团队',
      '有成功项目案例',
      '承诺技术服务质量'
    ],
    color: 'green'
  },
  {
    icon: Globe,
    title: '技术联盟',
    description: '技术合作，共同构建生态系统',
    benefits: [
      '技术资源共享',
      '联合研发机会',
      '品牌合作推广',
      '优先技术支持'
    ],
    requirements: [
      '在相关领域有技术优势',
      '产品具有互补性',
      '具备研发能力',
      '认同合作理念'
    ],
    color: 'purple'
  },
  {
    icon: Award,
    title: '咨询服务商',
    description: '为客户提供专业咨询和实施服务',
    benefits: [
      '咨询费用分成',
      '专业培训支持',
      '项目推荐机会',
      '品牌背书支持'
    ],
    requirements: [
      '具备行业咨询经验',
      '拥有专业团队',
      '有成功案例',
      '服务质量保证'
    ],
    color: 'orange'
  }
];

const partnerBenefits = [
  {
    icon: TrendingUp,
    title: '业务增长',
    description: '通过合作扩展业务范围，增加收入来源',
    details: '平均合作伙伴业务增长30%+'
  },
  {
    icon: Shield,
    title: '技术支持',
    description: '获得专业的技术支持和培训服务',
    details: '24/7技术支持，定期培训更新'
  },
  {
    icon: Star,
    title: '品牌价值',
    description: '与知名品牌合作，提升企业影响力',
    details: '共同品牌推广，提升市场地位'
  },
  {
    icon: Handshake,
    title: '长期合作',
    description: '建立长期稳定的合作关系',
    details: '多年期合作协议，稳定发展'
  }
];

const existingPartners = [
  {
    name: '某知名系统集成商',
    type: '系统集成商',
    logo: '/api/placeholder/100/60',
    achievement: '年销售额1000万+',
    testimonial: '与麒麟虚拟化的合作让我们在国产化项目中占据了优势地位，客户满意度和项目成功率都大幅提升。'
  },
  {
    name: '某区域分销商',
    type: '授权分销商',
    logo: '/api/placeholder/100/60',
    achievement: '覆盖200+企业客户',
    testimonial: '专业的产品和完善的支持体系，让我们能够为客户提供优质的虚拟化解决方案。'
  },
  {
    name: '某咨询公司',
    type: '咨询服务商',
    logo: '/api/placeholder/100/60',
    achievement: '完成50+咨询项目',
    testimonial: '麒麟虚拟化的技术实力和服务质量为我们的咨询业务提供了强有力的支撑。'
  }
];

const supportPrograms = [
  {
    title: '销售培训',
    description: '产品知识、销售技巧、竞争分析等全方位培训',
    icon: Users,
    frequency: '每月一次'
  },
  {
    title: '技术认证',
    description: '专业技术认证，提升服务能力和客户信任度',
    icon: Award,
    frequency: '按需安排'
  },
  {
    title: '市场支持',
    description: '联合市场活动、宣传材料、线索共享',
    icon: TrendingUp,
    frequency: '持续进行'
  },
  {
    title: '技术支持',
    description: '项目支持、技术咨询、问题解决',
    icon: Shield,
    frequency: '7×24小时'
  }
];

export default function PartnersPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <section className="bg-white border-b">
        <div className="container mx-auto px-4 py-16">
          <div className="max-w-4xl mx-auto text-center">
            <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-200 mb-4">
              合作伙伴计划
            </Badge>
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              携手共进，共创未来
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              加入麒麟虚拟化合作伙伴生态，共同推动国产虚拟化技术发展，实现互利共赢
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg">
                申请合作
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
              <Button size="lg" variant="outline">
                了解更多
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Partner Types */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">合作方式</h2>
            <p className="text-gray-600">多种合作模式，总有一款适合您</p>
          </div>

          <div className="grid md:grid-cols-2 gap-8">
            {partnerTypes.map((type, index) => (
              <Card key={index} className="group hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex items-center space-x-4 mb-4">
                    <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${
                      type.color === 'blue' ? 'bg-blue-100 text-blue-600' :
                      type.color === 'green' ? 'bg-green-100 text-green-600' :
                      type.color === 'purple' ? 'bg-purple-100 text-purple-600' :
                      'bg-orange-100 text-orange-600'
                    }`}>
                      <type.icon className="w-6 h-6" />
                    </div>
                    <div>
                      <CardTitle className="text-xl">{type.title}</CardTitle>
                      <CardDescription>{type.description}</CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid md:grid-cols-2 gap-6">
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-3">合作收益</h4>
                      <ul className="space-y-2">
                        {type.benefits.map((benefit, idx) => (
                          <li key={idx} className="flex items-start space-x-2">
                            <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                            <span className="text-sm text-gray-600">{benefit}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-3">申请要求</h4>
                      <ul className="space-y-2">
                        {type.requirements.map((req, idx) => (
                          <li key={idx} className="flex items-start space-x-2">
                            <div className="w-4 h-4 border-2 border-gray-300 rounded-sm mt-0.5 flex-shrink-0"></div>
                            <span className="text-sm text-gray-600">{req}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                  <div className="mt-6 pt-6 border-t">
                    <Button className="w-full" variant="outline">
                      申请 {type.title}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Partner Benefits */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">合作优势</h2>
            <p className="text-gray-600">为什么选择与我们合作</p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {partnerBenefits.map((benefit, index) => (
              <Card key={index} className="text-center group hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4 group-hover:bg-blue-200 transition-colors">
                    <benefit.icon className="w-8 h-8 text-blue-600" />
                  </div>
                  <CardTitle className="text-lg">{benefit.title}</CardTitle>
                  <CardDescription>{benefit.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-sm text-blue-600 font-medium">
                    {benefit.details}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Support Programs */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">支持计划</h2>
            <p className="text-gray-600">全方位支持体系，助力合作伙伴成功</p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {supportPrograms.map((program, index) => (
              <Card key={index} className="text-center">
                <CardHeader>
                  <div className="mx-auto w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-4">
                    <program.icon className="w-6 h-6 text-green-600" />
                  </div>
                  <CardTitle className="text-lg">{program.title}</CardTitle>
                  <CardDescription>{program.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <Badge className="bg-green-100 text-green-800">
                    {program.frequency}
                  </Badge>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Existing Partners */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">成功案例</h2>
            <p className="text-gray-600">听听我们的合作伙伴怎么说</p>
          </div>

          <div className="grid lg:grid-cols-3 gap-8">
            {existingPartners.map((partner, index) => (
              <Card key={index} className="p-6">
                <CardContent className="pt-0">
                  <div className="flex items-center space-x-4 mb-4">
                    <img 
                      src={partner.logo} 
                      alt={partner.name}
                      className="h-12 object-contain"
                    />
                    <div>
                      <div className="font-semibold text-gray-900">{partner.name}</div>
                      <Badge variant="outline" className="text-xs">
                        {partner.type}
                      </Badge>
                    </div>
                  </div>
                  <div className="text-2xl font-bold text-blue-600 mb-2">
                    {partner.achievement}
                  </div>
                  <blockquote className="text-gray-600 italic">
                    "{partner.testimonial}"
                  </blockquote>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Application Form */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">申请成为合作伙伴</h2>
              <p className="text-gray-600">填写申请表，我们将尽快与您联系</p>
            </div>

            <div className="grid lg:grid-cols-3 gap-8">
              <div className="lg:col-span-2">
                <Card>
                  <CardHeader>
                    <CardTitle>合作伙伴申请表</CardTitle>
                    <CardDescription>请详细填写以下信息</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="grid md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="company">公司名称 *</Label>
                        <Input id="company" placeholder="请输入公司名称" required />
                      </div>
                      <div>
                        <Label htmlFor="contact">联系人 *</Label>
                        <Input id="contact" placeholder="请输入联系人姓名" required />
                      </div>
                    </div>

                    <div className="grid md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="email">邮箱 *</Label>
                        <Input id="email" type="email" placeholder="请输入邮箱地址" required />
                      </div>
                      <div>
                        <Label htmlFor="phone">电话 *</Label>
                        <Input id="phone" placeholder="请输入联系电话" required />
                      </div>
                    </div>

                    <div className="grid md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="partnerType">合作类型 *</Label>
                        <Select>
                          <SelectTrigger>
                            <SelectValue placeholder="请选择合作类型" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="distributor">授权分销商</SelectItem>
                            <SelectItem value="integrator">系统集成商</SelectItem>
                            <SelectItem value="alliance">技术联盟</SelectItem>
                            <SelectItem value="consultant">咨询服务商</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label htmlFor="region">业务区域</Label>
                        <Select>
                          <SelectTrigger>
                            <SelectValue placeholder="请选择主要业务区域" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="north">华北地区</SelectItem>
                            <SelectItem value="east">华东地区</SelectItem>
                            <SelectItem value="south">华南地区</SelectItem>
                            <SelectItem value="west">西部地区</SelectItem>
                            <SelectItem value="national">全国</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="experience">公司简介和相关经验 *</Label>
                      <Textarea 
                        id="experience" 
                        placeholder="请简要介绍您的公司背景、业务范围、相关经验等"
                        rows={4}
                        required 
                      />
                    </div>

                    <div>
                      <Label htmlFor="resources">现有资源和客户情况</Label>
                      <Textarea 
                        id="resources" 
                        placeholder="请介绍您现有的客户资源、技术团队、销售网络等情况"
                        rows={3}
                      />
                    </div>

                    <div>
                      <Label htmlFor="expectations">合作期望</Label>
                      <Textarea 
                        id="expectations" 
                        placeholder="请描述您对合作的期望和计划"
                        rows={3}
                      />
                    </div>

                    <Button className="w-full" size="lg">
                      <FileText className="mr-2 h-4 w-4" />
                      提交申请
                    </Button>
                  </CardContent>
                </Card>
              </div>

              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>联系我们</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center space-x-3">
                      <Mail className="w-4 h-4 text-blue-600" />
                      <div>
                        <div className="font-medium"><EMAIL></div>
                        <div className="text-sm text-gray-600">合作伙伴专用邮箱</div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      <Phone className="w-4 h-4 text-green-600" />
                      <div>
                        <div className="font-medium">************</div>
                        <div className="text-sm text-gray-600">合作热线</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>申请流程</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-start space-x-3">
                        <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center text-xs font-semibold text-blue-600">1</div>
                        <div>
                          <div className="font-medium">提交申请</div>
                          <div className="text-sm text-gray-600">填写并提交申请表</div>
                        </div>
                      </div>
                      <div className="flex items-start space-x-3">
                        <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center text-xs font-semibold text-blue-600">2</div>
                        <div>
                          <div className="font-medium">资质审核</div>
                          <div className="text-sm text-gray-600">我们将评估您的申请</div>
                        </div>
                      </div>
                      <div className="flex items-start space-x-3">
                        <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center text-xs font-semibold text-blue-600">3</div>
                        <div>
                          <div className="font-medium">面谈交流</div>
                          <div className="text-sm text-gray-600">深入沟通合作细节</div>
                        </div>
                      </div>
                      <div className="flex items-start space-x-3">
                        <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center text-xs font-semibold text-blue-600">4</div>
                        <div>
                          <div className="font-medium">签署协议</div>
                          <div className="text-sm text-gray-600">正式建立合作关系</div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}