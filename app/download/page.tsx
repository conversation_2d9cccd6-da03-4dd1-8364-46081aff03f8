import { Metadata } from 'next';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Download, CheckCircle, Monitor, Cpu, HardDrive, Shield } from 'lucide-react';

export const metadata: Metadata = {
  title: '免费试用 - 麒麟虚拟化',
  description: '免费试用麒麟虚拟化软件30天，无需信用卡，体验完整功能。立即下载开始使用。',
  keywords: '麒麟虚拟化,免费试用,下载,30天试用'
};

export default function DownloadPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <section className="bg-white border-b">
        <div className="container mx-auto px-4 py-16">
          <div className="max-w-4xl mx-auto text-center">
            <Badge className="bg-green-100 text-green-800 hover:bg-green-200 mb-4">
              免费试用
            </Badge>
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              免费试用 30 天
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              体验完整功能，无需信用卡，立即开始使用麒麟虚拟化
            </p>
            <div className="flex items-center justify-center space-x-8 text-sm text-gray-600">
              <div className="flex items-center">
                <CheckCircle className="w-4 h-4 mr-2 text-green-500" />
                30天免费试用
              </div>
              <div className="flex items-center">
                <CheckCircle className="w-4 h-4 mr-2 text-green-500" />
                完整功能体验
              </div>
              <div className="flex items-center">
                <CheckCircle className="w-4 h-4 mr-2 text-green-500" />
                无需信用卡
              </div>
            </div>
          </div>
        </div>
      </section>

      <div className="container mx-auto px-4 py-16">
        <div className="max-w-6xl mx-auto">
          <div className="grid lg:grid-cols-3 gap-8">
            {/* Download Form */}
            <div className="lg:col-span-2">
              <Card className="shadow-lg">
                <CardHeader>
                  <CardTitle className="text-2xl">下载麒麟虚拟化</CardTitle>
                  <CardDescription>
                    请填写以下信息以开始下载试用版本
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="firstName">姓名 *</Label>
                      <Input id="firstName" placeholder="请输入您的姓名" required />
                    </div>
                    <div>
                      <Label htmlFor="lastName">姓氏 *</Label>
                      <Input id="lastName" placeholder="请输入您的姓氏" required />
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="email">电子邮箱 *</Label>
                    <Input id="email" type="email" placeholder="请输入您的邮箱地址" required />
                  </div>

                  <div>
                    <Label htmlFor="company">公司/组织</Label>
                    <Input id="company" placeholder="请输入您的公司或组织名称" />
                  </div>

                  <div>
                    <Label htmlFor="useCase">使用场景</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="请选择您的使用场景" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="personal">个人使用</SelectItem>
                        <SelectItem value="business">商业使用</SelectItem>
                        <SelectItem value="education">教育研究</SelectItem>
                        <SelectItem value="development">软件开发</SelectItem>
                        <SelectItem value="testing">测试验证</SelectItem>
                        <SelectItem value="other">其他</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="os">麒麟OS版本</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="请选择您的麒麟OS版本" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="kylin-desktop-v10">麒麟桌面版 V10</SelectItem>
                        <SelectItem value="kylin-desktop-v4">麒麟桌面版 V4</SelectItem>
                        <SelectItem value="kylin-server-v10">麒麟服务器版 V10</SelectItem>
                        <SelectItem value="other">其他版本</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-4">
                    <div className="flex items-center space-x-2">
                      <Checkbox id="terms" required />
                      <Label htmlFor="terms" className="text-sm">
                        我同意<a href="/terms" className="text-blue-600 hover:underline">服务条款</a>
                        和<a href="/privacy" className="text-blue-600 hover:underline">隐私政策</a>
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="newsletter" />
                      <Label htmlFor="newsletter" className="text-sm">
                        我希望接收产品更新和技术资讯
                      </Label>
                    </div>
                  </div>

                  <Button className="w-full bg-blue-600 hover:bg-blue-700 text-lg py-6">
                    <Download className="mr-2 h-5 w-5" />
                    立即下载试用版
                  </Button>
                </CardContent>
              </Card>
            </div>

            {/* System Requirements & Info */}
            <div className="space-y-6">
              {/* System Requirements */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Monitor className="w-5 h-5 mr-2" />
                    系统要求
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex items-start space-x-2">
                      <Cpu className="w-4 h-4 mt-1 text-blue-600" />
                      <div>
                        <div className="font-medium">处理器</div>
                        <div className="text-sm text-gray-600">Intel/AMD x86_64 处理器</div>
                      </div>
                    </div>
                    <div className="flex items-start space-x-2">
                      <HardDrive className="w-4 h-4 mt-1 text-blue-600" />
                      <div>
                        <div className="font-medium">内存</div>
                        <div className="text-sm text-gray-600">最低 4GB RAM，推荐 8GB+</div>
                      </div>
                    </div>
                    <div className="flex items-start space-x-2">
                      <HardDrive className="w-4 h-4 mt-1 text-blue-600" />
                      <div>
                        <div className="font-medium">存储</div>
                        <div className="text-sm text-gray-600">至少 20GB 可用空间</div>
                      </div>
                    </div>
                    <div className="flex items-start space-x-2">
                      <Shield className="w-4 h-4 mt-1 text-blue-600" />
                      <div>
                        <div className="font-medium">系统</div>
                        <div className="text-sm text-gray-600">麒麟操作系统 V4.0+</div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Trial Info */}
              <Card>
                <CardHeader>
                  <CardTitle>试用信息</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="w-4 h-4 text-green-500" />
                    <span className="text-sm">30天完整功能体验</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="w-4 h-4 text-green-500" />
                    <span className="text-sm">支持所有虚拟化功能</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="w-4 h-4 text-green-500" />
                    <span className="text-sm">包含技术支持</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="w-4 h-4 text-green-500" />
                    <span className="text-sm">无功能限制</span>
                  </div>
                </CardContent>
              </Card>

              {/* Support */}
              <Card>
                <CardHeader>
                  <CardTitle>需要帮助？</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-600 mb-4">
                    如果您在下载或安装过程中遇到问题，我们的技术支持团队随时为您提供帮助。
                  </p>
                  <div className="space-y-2">
                    <Button variant="outline" className="w-full">
                      查看安装指南
                    </Button>
                    <Button variant="outline" className="w-full">
                      联系技术支持
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}