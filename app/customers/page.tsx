import { Metadata } from 'next';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Star, 
  Quote, 
  Building, 
  Users, 
  TrendingUp,
  Shield,
  Zap,
  Globe,
  ArrowRight,
  Play,
  Download
} from 'lucide-react';
import Link from 'next/link';

export const metadata: Metadata = {
  title: '客户案例 - 麒麟虚拟化',
  description: '了解知名企业和政府机构如何使用麒麟虚拟化解决方案提升效率和安全性。',
  keywords: '客户案例,成功案例,企业用户,政府机构,麒麟虚拟化'
};

const customerLogos = [
  { name: '中国银行', logo: '/api/placeholder/120/60' },
  { name: '国家电网', logo: '/api/placeholder/120/60' },
  { name: '中国移动', logo: '/api/placeholder/120/60' },
  { name: '华为技术', logo: '/api/placeholder/120/60' },
  { name: '中石油', logo: '/api/placeholder/120/60' },
  { name: '清华大学', logo: '/api/placeholder/120/60' },
  { name: '航天科技', logo: '/api/placeholder/120/60' },
  { name: '中科院', logo: '/api/placeholder/120/60' }
];

const featuredCases = [
  {
    id: 1,
    company: '某大型银行',
    industry: '金融服务',
    logo: '/api/placeholder/80/80',
    title: '构建安全可控的办公虚拟化环境',
    challenge: '需要在麒麟OS环境下运行Windows应用，确保数据安全和业务连续性',
    solution: '部署麒麟虚拟化企业版，实现1000+员工的虚拟桌面环境',
    results: [
      '安全性提升95%',
      '运维成本降低40%',
      '系统稳定性99.9%',
      '用户满意度98%'
    ],
    testimonial: '麒麟虚拟化帮助我们在确保安全合规的前提下，顺利完成了信创改造，员工可以无缝使用熟悉的Windows应用。',
    author: '张总监',
    position: '信息技术部总监',
    deploymentSize: '1000+用户',
    industry_icon: Building,
    tags: ['金融', '企业版', '大规模部署']
  },
  {
    id: 2,
    company: '某省政府机构',
    industry: '政府机构',
    logo: '/api/placeholder/80/80',
    title: '政务系统国产化改造项目',
    challenge: '政务系统需要从Windows平台迁移到麒麟OS，保持业务连续性',
    solution: '采用麒麟虚拟化解决方案，渐进式迁移政务应用',
    results: [
      '迁移周期缩短60%',
      '系统兼容性100%',
      '数据安全零事故',
      '培训成本降低50%'
    ],
    testimonial: '通过麒麟虚拟化，我们实现了平滑的国产化改造，既满足了政策要求，又保证了业务不中断。',
    author: '李主任',
    position: '信息化建设主任',
    deploymentSize: '500+用户',
    industry_icon: Shield,
    tags: ['政府', '国产化', '迁移项目']
  },
  {
    id: 3,
    company: '某知名制造企业',
    industry: '制造业',
    logo: '/api/placeholder/80/80',
    title: '智能制造数字化转型',
    challenge: '工业软件需要在麒麟OS环境下稳定运行，支持生产管理',
    solution: '部署专业版虚拟化环境，优化工业软件性能',
    results: [
      '生产效率提升30%',
      '系统响应时间< 2秒',
      '硬件成本节省35%',
      '维护工作量减少45%'
    ],
    testimonial: '麒麟虚拟化的高性能和稳定性让我们的工业软件运行如丝般顺滑，为数字化转型奠定了坚实基础。',
    author: '王工程师',
    position: '信息化总工程师',
    deploymentSize: '200+用户',
    industry_icon: Zap,
    tags: ['制造业', '专业版', '工业软件']
  }
];

const testimonials = [
  {
    content: '麒麟虚拟化是我们见过的最好的国产虚拟化解决方案，性能和稳定性都超出了预期。',
    author: '陈总',
    company: '某科技公司',
    position: 'CTO',
    rating: 5,
    avatar: '/api/placeholder/50/50'
  },
  {
    content: '迁移过程非常顺利，技术支持团队专业且响应迅速，为我们节省了大量时间和成本。',
    author: '刘经理',
    company: '某国企',
    position: '信息部经理',
    rating: 5,
    avatar: '/api/placeholder/50/50'
  },
  {
    content: '安全性和合规性都很出色，完全满足我们行业的严格要求。',
    author: '孙主任',
    company: '某政府部门',
    position: '技术主任',
    rating: 5,
    avatar: '/api/placeholder/50/50'
  },
  {
    content: '用户体验非常好，员工几乎感觉不到在使用虚拟化环境，过渡很平滑。',
    author: '周总监',
    company: '某教育机构',
    position: '网络中心总监',
    rating: 5,
    avatar: '/api/placeholder/50/50'
  }
];

const industryStats = [
  {
    industry: '金融服务',
    customers: '50+',
    icon: Building,
    growth: '+120%'
  },
  {
    industry: '政府机构',
    customers: '80+',
    icon: Shield,
    growth: '+200%'
  },
  {
    industry: '教育科研',
    customers: '100+',
    icon: Globe,
    growth: '+150%'
  },
  {
    industry: '制造业',
    customers: '60+',
    icon: Zap,
    growth: '+180%'
  }
];

export default function CustomersPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <section className="bg-white border-b">
        <div className="container mx-auto px-4 py-16">
          <div className="max-w-4xl mx-auto text-center">
            <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-200 mb-4">
              客户案例
            </Badge>
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              值得信赖的选择
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              全国数百家知名企业和政府机构的共同选择，助力数字化转型和国产化改造
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" asChild>
                <Link href="/download">
                  免费试用
                  <Download className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button size="lg" variant="outline" asChild>
                <Link href="/support/contact">
                  联系销售
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Customer Logos */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">知名客户</h2>
            <p className="text-gray-600">已服务数百家企业和机构</p>
          </div>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-8 items-center opacity-70">
            {customerLogos.map((customer, index) => (
              <div key={index} className="flex items-center justify-center">
                <img 
                  src={customer.logo} 
                  alt={customer.name}
                  className="h-12 max-w-full object-contain grayscale hover:grayscale-0 transition-all duration-300"
                />
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Industry Stats */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">行业覆盖</h2>
            <p className="text-gray-600">在各个关键行业都有成功应用</p>
          </div>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {industryStats.map((stat, index) => (
              <Card key={index} className="text-center group hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4 group-hover:bg-blue-200 transition-colors">
                    <stat.icon className="w-8 h-8 text-blue-600" />
                  </div>
                  <CardTitle className="text-xl">{stat.industry}</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold text-gray-900 mb-2">{stat.customers}</div>
                  <div className="text-sm text-gray-600 mb-2">客户数量</div>
                  <Badge className="bg-green-100 text-green-800">
                    同比增长 {stat.growth}
                  </Badge>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Featured Case Studies */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">典型案例</h2>
            <p className="text-gray-600">深入了解客户如何通过麒麟虚拟化实现业务目标</p>
          </div>

          <div className="space-y-12">
            {featuredCases.map((case_study, index) => (
              <Card key={case_study.id} className="overflow-hidden">
                <div className="grid lg:grid-cols-5 gap-0">
                  <div className="lg:col-span-3 p-8">
                    <div className="flex items-start space-x-4 mb-6">
                      <img 
                        src={case_study.logo} 
                        alt={case_study.company}
                        className="w-16 h-16 rounded-lg object-cover"
                      />
                      <div>
                        <h3 className="text-2xl font-bold text-gray-900 mb-2">{case_study.title}</h3>
                        <div className="flex items-center space-x-4">
                          <Badge variant="outline">{case_study.company}</Badge>
                          <Badge variant="outline">{case_study.industry}</Badge>
                          <Badge variant="outline">{case_study.deploymentSize}</Badge>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-6">
                      <div>
                        <h4 className="font-semibold text-gray-900 mb-2">挑战</h4>
                        <p className="text-gray-600">{case_study.challenge}</p>
                      </div>
                      
                      <div>
                        <h4 className="font-semibold text-gray-900 mb-2">解决方案</h4>
                        <p className="text-gray-600">{case_study.solution}</p>
                      </div>

                      <div className="grid md:grid-cols-2 gap-4">
                        {case_study.results.map((result, idx) => (
                          <div key={idx} className="flex items-center space-x-2">
                            <TrendingUp className="w-4 h-4 text-green-600" />
                            <span className="text-sm font-medium text-gray-900">{result}</span>
                          </div>
                        ))}
                      </div>

                      <div className="flex flex-wrap gap-2">
                        {case_study.tags.map((tag, idx) => (
                          <Badge key={idx} className="bg-blue-100 text-blue-800">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>

                  <div className="lg:col-span-2 bg-gradient-to-br from-blue-50 to-purple-50 p-8 flex flex-col justify-center">
                    <Quote className="w-8 h-8 text-blue-600 mb-4" />
                    <blockquote className="text-lg italic text-gray-700 mb-6">
                      "{case_study.testimonial}"
                    </blockquote>
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
                        <span className="text-white font-semibold text-sm">
                          {case_study.author.charAt(0)}
                        </span>
                      </div>
                      <div>
                        <div className="font-semibold text-gray-900">{case_study.author}</div>
                        <div className="text-sm text-gray-600">{case_study.position}</div>
                        <div className="text-sm text-gray-500">{case_study.company}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Customer Testimonials */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">客户评价</h2>
            <p className="text-gray-600">听听客户怎么说</p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {testimonials.map((testimonial, index) => (
              <Card key={index} className="p-6 text-center">
                <CardContent className="pt-0">
                  <div className="flex justify-center mb-4">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star key={i} className="w-4 h-4 text-yellow-400 fill-current" />
                    ))}
                  </div>
                  <blockquote className="text-gray-700 mb-4 italic">
                    "{testimonial.content}"
                  </blockquote>
                  <div className="flex items-center justify-center space-x-3">
                    <img 
                      src={testimonial.avatar}
                      alt={testimonial.author}
                      className="w-8 h-8 rounded-full object-cover"
                    />
                    <div className="text-left">
                      <div className="font-semibold text-gray-900 text-sm">{testimonial.author}</div>
                      <div className="text-xs text-gray-600">{testimonial.position}</div>
                      <div className="text-xs text-gray-500">{testimonial.company}</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gradient-to-r from-blue-600 to-purple-700 text-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              加入成功客户的行列
            </h2>
            <p className="text-xl mb-8 opacity-90">
              了解麒麟虚拟化如何助力您的数字化转型和国产化改造
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-100">
                <Download className="mr-2 h-5 w-5" />
                免费试用
              </Button>
              <Button size="lg" variant="outline" className="border-white text-white hover:bg-white/10">
                预约演示
              </Button>
              <Button size="lg" variant="outline" className="border-white text-white hover:bg-white/10">
                获取方案
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}