# 贡献指南 🤝

感谢您对Quintar虚拟化产品官网项目的关注！我们欢迎所有形式的贡献，包括但不限于：

- 🐛 Bug 报告和修复
- ✨ 新功能建议和实现
- 📖 文档改进
- 🎨 UI/UX 优化
- 🧪 测试用例添加
- 🌍 国际化和本地化

## 📋 贡献流程

### 1. 准备工作

#### Fork 和 Clone
```bash
# Fork 项目到你的 GitHub 账户
# 然后克隆到本地
git clone https://github.com/your-username/quintar.git
cd quintar

# 添加原始仓库作为上游
git remote add upstream https://github.com/original-username/quintar.git
```

#### 安装依赖
```bash
npm install
```

#### 启动开发环境
```bash
npm run dev
```

### 2. 开发流程

#### 创建分支
```bash
# 从 main 分支创建新的功能分支
git checkout main
git pull upstream main
git checkout -b feature/your-feature-name

# 分支命名规范：
# feature/功能名称     - 新功能
# fix/问题描述        - Bug修复  
# docs/文档更新       - 文档更新
# style/样式改进      - 样式调整
# refactor/重构描述   - 代码重构
# test/测试描述       - 测试相关
```

#### 编写代码
- 遵循项目的代码规范和最佳实践
- 为新功能编写相应的测试用例
- 确保所有测试都能通过
- 更新相关文档

#### 提交代码
使用 [Conventional Commits](https://www.conventionalcommits.org/) 规范：

```bash
# 提交格式: type(scope): description
git commit -m "feat(components): add responsive navigation menu"
git commit -m "fix(api): resolve health check timeout issue"
git commit -m "docs(readme): update installation instructions"
```

**提交类型说明：**
- `feat`: 新功能
- `fix`: Bug修复
- `docs`: 文档更新
- `style`: 代码格式调整（不影响代码逻辑）
- `refactor`: 代码重构
- `test`: 添加或修改测试
- `chore`: 构建过程或辅助工具的变动

### 3. 质量检查

#### 运行检查命令
```bash
# 代码质量检查
npm run lint

# 类型检查
npx tsc --noEmit

# 运行测试
npm test

# 构建检查
npm run build
```

#### 修复问题
```bash
# 自动修复 ESLint 问题
npm run lint:fix

# 格式化代码 (如果配置了 Prettier)
npm run format
```

### 4. 提交 Pull Request

#### 推送分支
```bash
git push origin feature/your-feature-name
```

#### 创建 Pull Request
1. 在 GitHub 上创建 Pull Request
2. 填写详细的描述，包括：
   - 🎯 **目标**: 说明这个 PR 的目的
   - 🔄 **改动**: 列出主要的改动内容
   - 🧪 **测试**: 说明如何测试这些改动
   - 📸 **截图**: 如果有UI改动，请提供截图
   - ⚠️ **注意事项**: 需要特别注意的地方

#### PR模板示例
```markdown
## 🎯 目标
简要描述这个 PR 的目的和解决的问题

## 🔄 主要改动
- [ ] 新增了响应式导航组件
- [ ] 优化了移动端用户体验
- [ ] 添加了相关测试用例

## 🧪 测试说明
- [ ] 所有现有测试通过
- [ ] 添加了新的测试用例
- [ ] 手动测试在多个设备上正常工作

## 📸 截图
如有UI改动，请提供截图

## ⚠️ 注意事项
需要特别关注的地方
```

## 🧪 测试指南

### 测试类型
1. **单元测试**: 测试单个组件或函数
2. **集成测试**: 测试组件间的交互
3. **端到端测试**: 测试完整的用户流程

### 编写测试

#### 组件测试示例
```tsx
// __tests__/components/button.test.tsx
import { render, screen, fireEvent } from '@testing-library/react'
import { Button } from '@/components/ui/button'

describe('Button Component', () => {
  it('renders correctly', () => {
    render(<Button>Click me</Button>)
    expect(screen.getByText('Click me')).toBeInTheDocument()
  })

  it('handles click events', () => {
    const handleClick = jest.fn()
    render(<Button onClick={handleClick}>Click me</Button>)
    fireEvent.click(screen.getByText('Click me'))
    expect(handleClick).toHaveBeenCalledTimes(1)
  })
})
```

#### API测试示例
```tsx
// __tests__/api/health.test.ts
import { GET } from '@/app/api/health/route'

describe('/api/health', () => {
  it('returns health status', async () => {
    const response = await GET()
    const data = await response.json()
    
    expect(response.status).toBe(200)
    expect(data.status).toBe('ok')
  })
})
```

### 测试覆盖率
确保维持足够的测试覆盖率：
- 语句覆盖率: >= 80%
- 分支覆盖率: >= 75%
- 函数覆盖率: >= 80%

## 🎨 代码规范

### TypeScript
- 使用 TypeScript 进行类型安全
- 定义清晰的接口和类型
- 避免使用 `any` 类型

```tsx
// ✅ 好的示例
interface ButtonProps {
  variant?: 'primary' | 'secondary' | 'outline'
  size?: 'sm' | 'md' | 'lg'
  children: React.ReactNode
  onClick?: () => void
}

// ❌ 避免的示例
interface ButtonProps {
  props: any
}
```

### React 组件
- 使用函数组件和 Hooks
- 保持组件的单一职责
- 正确使用 useCallback 和 useMemo 优化性能

```tsx
// ✅ 好的示例
import { memo, useCallback } from 'react'

interface ComponentProps {
  data: DataType[]
  onItemClick: (id: string) => void
}

export const Component = memo<ComponentProps>(({ data, onItemClick }) => {
  const handleClick = useCallback((id: string) => {
    onItemClick(id)
  }, [onItemClick])

  return (
    // JSX
  )
})
```

### CSS/Tailwind
- 优先使用 Tailwind CSS 类名
- 保持一致的间距和颜色系统
- 使用响应式设计类名

```tsx
// ✅ 好的示例
<div className="flex flex-col gap-4 p-6 bg-white dark:bg-gray-900 rounded-lg shadow-md md:flex-row md:items-center">

// ❌ 避免的示例  
<div style={{ display: 'flex', gap: '16px', padding: '24px' }}>
```

## 📖 文档规范

### 代码注释
```tsx
/**
 * 计算两个日期之间的天数差
 * @param startDate 开始日期
 * @param endDate 结束日期
 * @returns 天数差
 */
export function getDateDifference(startDate: Date, endDate: Date): number {
  const timeDiff = endDate.getTime() - startDate.getTime()
  return Math.ceil(timeDiff / (1000 * 3600 * 24))
}
```

### README 更新
如果你的改动影响了项目的使用方式，请更新相关文档：
- 新功能的使用说明
- API 变更说明
- 安装或配置步骤的更新

## 🚀 发布流程

### 版本规范
项目使用 [Semantic Versioning](https://semver.org/):
- **MAJOR**: 不兼容的 API 修改
- **MINOR**: 向后兼容的功能新增
- **PATCH**: 向后兼容的错误修复

### 发布检查清单
- [ ] 所有测试通过
- [ ] 文档已更新
- [ ] CHANGELOG.md 已更新
- [ ] 版本号已更新
- [ ] 构建成功
- [ ] 部署测试通过

## 🐛 Bug 报告

### 报告 Bug 前的检查
1. 搜索现有的 Issues，确认问题未被报告
2. 确认问题在最新版本中仍然存在
3. 准备复现步骤和相关信息

### Bug 报告模板
```markdown
**Bug 描述**
清晰简洁地描述遇到的问题

**复现步骤**
1. 访问页面 '...'
2. 点击按钮 '...'
3. 向下滚动到 '...'
4. 观察错误

**期望行为**
描述你期望发生的行为

**实际行为**
描述实际发生的行为

**截图**
如果适用，添加截图来帮助解释问题

**环境信息**
- 操作系统: [例如 macOS 12.0]
- 浏览器: [例如 Chrome 96.0]
- 设备: [例如 iPhone 12, Desktop]
- 屏幕分辨率: [例如 1920x1080]

**附加信息**
添加任何其他相关的上下文信息
```

## ✨ 功能建议

### 建议新功能前的考虑
1. 这个功能是否符合项目的目标？
2. 是否有其他用户需要这个功能？
3. 是否可以通过现有功能实现？

### 功能建议模板
```markdown
**功能描述**
清晰简洁地描述建议的功能

**问题背景**
描述这个功能要解决的问题或满足的需求

**建议解决方案**
描述你希望如何实现这个功能

**替代方案**
描述你考虑过的其他解决方案

**附加信息**
添加任何其他相关的上下文、屏幕截图或示例
```

## 🌍 国际化贡献

如果你想帮助项目支持更多语言：

1. 检查 `lib/i18n/` 目录下的现有翻译文件
2. 创建新的语言文件或改进现有翻译
3. 确保翻译的准确性和一致性
4. 测试 UI 在不同语言下的显示效果

## 🎯 优先级指南

### 高优先级
- 🚨 安全漏洞修复
- 🐛 严重 Bug 修复
- ♿ 可访问性改进
- 📱 移动端体验优化

### 中优先级
- ✨ 用户请求的功能
- 🚀 性能优化
- 🧪 测试覆盖率提升
- 📖 文档完善

### 低优先级
- 🎨 UI/UX 微调
- 🔧 开发者体验改进
- 📦 依赖更新
- 💅 代码重构

## ❓ 获取帮助

如果你在贡献过程中遇到问题：

1. **查看文档**: 首先查看项目的 README 和相关文档
2. **搜索 Issues**: 搜索现有的 Issues 看是否有类似问题
3. **提出问题**: 在 [GitHub Discussions](https://github.com/your-repo/discussions) 中询问
4. **联系维护者**: 发邮件给项目维护者

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者！你们的贡献让这个项目变得更好。

---

再次感谢您的贡献！您的每一份努力都让这个项目变得更好。🚀