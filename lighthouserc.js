module.exports = {
  ci: {
    collect: {
      url: [
        'http://localhost:3000',
        'http://localhost:3000/products/personal',
        'http://localhost:3000/products/professional',
        'http://localhost:3000/products/enterprise',
        'http://localhost:3000/kylin-os',
        'http://localhost:3000/download',
        'http://localhost:3000/purchase',
        'http://localhost:3000/support',
        'http://localhost:3000/customers',
        'http://localhost:3000/partners'
      ],
      numberOfRuns: 3,
    },
    assert: {
      assertions: {
        'categories:performance': ['warn', { minScore: 0.8 }],
        'categories:accessibility': ['error', { minScore: 0.9 }],
        'categories:best-practices': ['warn', { minScore: 0.85 }],
        'categories:seo': ['error', { minScore: 0.9 }],
        'categories:pwa': 'off', // PWA 不是必需的
      },
    },
    upload: {
      target: 'temporary-public-storage',
    },
  },
}