#!/bin/bash

# 部署脚本
# 用法: ./scripts/deploy.sh [environment]

set -e

ENVIRONMENT=${1:-production}
PROJECT_NAME="quintar"

echo "🚀 开始Quintar部署到 $ENVIRONMENT 环境..."

# 检查是否在正确的分支
if [ "$ENVIRONMENT" = "production" ]; then
    EXPECTED_BRANCH="main"
else
    EXPECTED_BRANCH="develop"
fi

CURRENT_BRANCH=$(git branch --show-current)
if [ "$CURRENT_BRANCH" != "$EXPECTED_BRANCH" ]; then
    echo "❌ 错误: 当前分支是 $CURRENT_BRANCH，但需要在 $EXPECTED_BRANCH 分支进行 $ENVIRONMENT 部署"
    exit 1
fi

# 确保代码是最新的
echo "📦 获取最新代码..."
git pull origin $EXPECTED_BRANCH

# 安装依赖
echo "📦 安装依赖..."
npm ci

# 运行测试
echo "🧪 运行测试..."
npm test

# 运行 lint 检查
echo "🔍 运行代码检查..."
npm run lint

# 构建项目
echo "🔨 构建项目..."
npm run build

# Docker 部署
if command -v docker &> /dev/null; then
    echo "🐳 构建 Docker 镜像..."
    docker build -t $PROJECT_NAME:$ENVIRONMENT .
    
    echo "🚀 启动容器..."
    docker-compose down 2>/dev/null || true
    docker-compose up -d
    
    # 等待服务启动
    echo "⏳ 等待服务启动..."
    sleep 10
    
    # 健康检查
    echo "🏥 执行健康检查..."
    if curl -f http://localhost:3000/api/health; then
        echo "✅ 部署成功！服务正常运行"
    else
        echo "❌ 健康检查失败，请检查日志"
        docker-compose logs
        exit 1
    fi
else
    echo "⚠️  Docker 未安装，跳过容器化部署"
    echo "📝 请手动启动应用: npm start"
fi

echo "🎉 部署完成！"
echo "🌐 应用访问地址: http://localhost:3000"